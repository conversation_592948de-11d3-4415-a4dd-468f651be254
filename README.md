# 📄 智能文档翻译工具

一个功能完整的桌面端文档翻译工具，支持多种文档格式和翻译服务提供商。

## ✨ 主要特性

### 🔤 文本翻译
- 支持实时文本翻译
- 多种翻译服务提供商（Google、OpenAI、Azure等）
- 自动语言检测
- 翻译质量评估

### 📄 文档翻译
- 支持多种文档格式：PDF、DOCX、TXT、Markdown
- 智能文档解析和布局保持
- 批量翻译优化
- 多线程并发处理

### 🎨 用户界面
- 基于Gradio的现代化Web界面
- 响应式设计，支持多设备
- 实时预览和进度显示
- 直观的操作流程

### 📤 导出功能
- 多种导出格式：PDF、DOCX、TXT、Markdown
- 三种导出模式：仅翻译、双语对照、仅原文
- 保持原文档格式和布局
- 批量导出支持

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 8GB+ RAM（推荐）
- 网络连接（用于翻译API）

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/document-translator.git
cd document-translator
