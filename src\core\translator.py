#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译引擎核心模块
支持多种翻译服务提供商
"""

import asyncio
import aiohttp
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import time

@dataclass
class TranslationResult:
    """翻译结果"""
    original_text: str
    translated_text: str
    source_language: str
    target_language: str
    provider: str
    confidence: float = 0.0
    error: Optional[str] = None

class BaseTranslator(ABC):
    """翻译器基类"""
    
    def __init__(self, api_key: Optional[str] = None, **kwargs):
        self.api_key = api_key
        self.logger = logging.getLogger(self.__class__.__name__)
        self.session = None
    
    @abstractmethod
    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """翻译文本"""
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        pass
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

class GoogleTranslator(BaseTranslator):
    """Google翻译"""
    
    def __init__(self, api_key: Optional[str] = None):
        super().__init__(api_key)
        self.base_url = "https://translation.googleapis.com/language/translate/v2"
    
    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """使用Google翻译API翻译文本"""
        try:
            if not self.api_key:
                # 使用免费的Google翻译接口（有限制）
                return await self._translate_free(text, source_lang, target_lang)
            
            params = {
                'key': self.api_key,
                'q': text,
                'source': source_lang if source_lang != 'auto' else '',
                'target': target_lang,
                'format': 'text'
            }
            
            async with self.session.post(self.base_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    translation = data['data']['translations'][0]
                    
                    return TranslationResult(
                        original_text=text,
                        translated_text=translation['translatedText'],
                        source_language=translation.get('detectedSourceLanguage', source_lang),
                        target_language=target_lang,
                        provider='google',
                        confidence=1.0
                    )
                else:
                    error_msg = f"Google翻译API错误: {response.status}"
                    return TranslationResult(
                        original_text=text,
                        translated_text="",
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='google',
                        error=error_msg
                    )
                    
        except Exception as e:
            self.logger.error(f"Google翻译失败: {e}")
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider='google',
                error=str(e)
            )
    
    async def _translate_free(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """免费Google翻译接口"""
        # 这里可以实现免费的Google翻译接口
        # 注意：免费接口通常有使用限制
        url = "https://translate.googleapis.com/translate_a/single"
        params = {
            'client': 'gtx',
            'sl': source_lang,
            'tl': target_lang,
            'dt': 't',
            'q': text
        }
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    translated_text = ''.join([item[0] for item in data[0] if item[0]])
                    
                    return TranslationResult(
                        original_text=text,
                        translated_text=translated_text,
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='google_free',
                        confidence=0.8
                    )
        except Exception as e:
            self.logger.error(f"免费Google翻译失败: {e}")
        
        return TranslationResult(
            original_text=text,
            translated_text="",
            source_language=source_lang,
            target_language=target_lang,
            provider='google_free',
            error="翻译失败"
        )
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言"""
        return {
            'auto': '自动检测',
            'zh-CN': '中文(简体)',
            'zh-TW': '中文(繁体)',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语',
            'ar': '阿拉伯语'
        }

class OpenAITranslator(BaseTranslator):
    """OpenAI翻译"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        super().__init__(api_key)
        self.model = model
        self.base_url = "https://api.openai.com/v1/chat/completions"
    
    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """使用OpenAI API翻译文本"""
        try:
            # 构建提示词
            prompt = self._build_prompt(text, source_lang, target_lang)
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': self.model,
                'messages': [
                    {'role': 'system', 'content': '你是一个专业的翻译助手。'},
                    {'role': 'user', 'content': prompt}
                ],
                'temperature': 0.3,
                'max_tokens': 2000
            }
            
            async with self.session.post(self.base_url, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    translated_text = result['choices'][0]['message']['content'].strip()
                    
                    return TranslationResult(
                        original_text=text,
                        translated_text=translated_text,
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='openai',
                        confidence=0.9
                    )
                else:
                    error_msg = f"OpenAI API错误: {response.status}"
                    return TranslationResult(
                        original_text=text,
                        translated_text="",
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='openai',
                        error=error_msg
                    )
                    
        except Exception as e:
            self.logger.error(f"OpenAI翻译失败: {e}")
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider='openai',
                error=str(e)
            )
    
    def _build_prompt(self, text: str, source_lang: str, target_lang: str) -> str:
        """构建翻译提示词"""
        lang_map = {
            'zh-CN': '中文',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }
        
        target_lang_name = lang_map.get(target_lang, target_lang)
        
        return f"""请将以下文本翻译成{target_lang_name}，保持原文的格式和语义：

{text}

翻译要求：
1. 准确传达原文意思
2. 保持专业术语的准确性
3. 保持原文格式
4. 语言自然流畅

请只返回翻译结果，不要包含其他说明。"""
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言"""
        return {
            'auto': '自动检测',
            'zh-CN': '中文(简体)',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }

class TranslationEngine:
    """翻译引擎管理器"""
    
    def __init__(self, config):
        self.config = config
        self.translators = {}
        self.logger = logging.getLogger(__name__)
        self._init_translators()
    
    def _init_translators(self):
        """初始化翻译器"""
        # Google翻译
        google_key = self.config.get_api_key('google')
        self.translators['google'] = GoogleTranslator(google_key)
        
        # OpenAI翻译
        openai_key = self.config.get_api_key('openai')
        if openai_key:
            self.translators['openai'] = OpenAITranslator(openai_key)
        
        # 可以添加更多翻译器...
    
    async def translate_text(self, text: str, source_lang: str = 'auto', 
                           target_lang: str = 'zh-CN', provider: str = 'google') -> TranslationResult:
        """翻译单个文本"""
        if provider not in self.translators:
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider=provider,
                error=f"不支持的翻译提供商: {provider}"
            )
        
        translator = self.translators[provider]
        async with translator:
            return await translator.translate(text, source_lang, target_lang)
    
    async def translate_batch(self, texts: List[str], source_lang: str = 'auto',
                            target_lang: str = 'zh-CN', provider: str = 'google',
                            max_concurrent: int = 5) -> List[TranslationResult]:
        """批量翻译文本"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def translate_with_semaphore(text):
            async with semaphore:
                return await self.translate_text(text, source_lang, target_lang, provider)
        
        tasks = [translate_with_semaphore(text) for text in texts]
        return await asyncio.gather(*tasks)
    
    def get_available_providers(self) -> List[str]:
        """获取可用的翻译提供商"""
        return list(self.translators.keys())
    
    def get_supported_languages(self, provider: str) -> Dict[str, str]:
        """获取指定提供商支持的语言"""
        if provider in self.translators:
            return self.translators[provider].get_supported_languages()
        return {}