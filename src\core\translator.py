#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译引擎核心模块
支持多种翻译服务提供商
"""

import asyncio
import aiohttp
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import time

@dataclass
class TranslationResult:
    """翻译结果"""
    original_text: str
    translated_text: str
    source_language: str
    target_language: str
    provider: str
    confidence: float = 0.0
    error: Optional[str] = None

class BaseTranslator(ABC):
    """翻译器基类"""

    def __init__(self, api_key: Optional[str] = None, **kwargs):
        self.api_key = api_key
        self.logger = logging.getLogger(self.__class__.__name__)
        self.session = None

    @abstractmethod
    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """翻译文本"""
        pass

    @abstractmethod
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        pass

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

class GoogleTranslator(BaseTranslator):
    """Google翻译"""

    def __init__(self, api_key: Optional[str] = None):
        super().__init__(api_key)
        self.base_url = "https://translation.googleapis.com/language/translate/v2"

    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """使用Google翻译API翻译文本"""
        try:
            if not self.api_key:
                # 使用免费的Google翻译接口（有限制）
                return await self._translate_free(text, source_lang, target_lang)

            params = {
                'key': self.api_key,
                'q': text,
                'source': source_lang if source_lang != 'auto' else '',
                'target': target_lang,
                'format': 'text'
            }

            async with self.session.post(self.base_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    translation = data['data']['translations'][0]

                    return TranslationResult(
                        original_text=text,
                        translated_text=translation['translatedText'],
                        source_language=translation.get('detectedSourceLanguage', source_lang),
                        target_language=target_lang,
                        provider='google',
                        confidence=1.0
                    )
                else:
                    error_msg = f"Google翻译API错误: {response.status}"
                    return TranslationResult(
                        original_text=text,
                        translated_text="",
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='google',
                        error=error_msg
                    )

        except Exception as e:
            self.logger.error(f"Google翻译失败: {e}")
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider='google',
                error=str(e)
            )

    async def _translate_free(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """免费Google翻译接口"""
        # 这里可以实现免费的Google翻译接口
        # 注意：免费接口通常有使用限制
        url = "https://translate.googleapis.com/translate_a/single"
        params = {
            'client': 'gtx',
            'sl': source_lang,
            'tl': target_lang,
            'dt': 't',
            'q': text
        }

        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    translated_text = ''.join([item[0] for item in data[0] if item[0]])

                    return TranslationResult(
                        original_text=text,
                        translated_text=translated_text,
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='google_free',
                        confidence=0.8
                    )
        except Exception as e:
            self.logger.error(f"免费Google翻译失败: {e}")

        return TranslationResult(
            original_text=text,
            translated_text="",
            source_language=source_lang,
            target_language=target_lang,
            provider='google_free',
            error="翻译失败"
        )

    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言"""
        return {
            'auto': '自动检测',
            'zh-CN': '中文(简体)',
            'zh-TW': '中文(繁体)',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语',
            'ar': '阿拉伯语'
        }

class OpenAITranslator(BaseTranslator):
    """OpenAI翻译"""

    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        super().__init__(api_key)
        self.model = model
        self.base_url = "https://api.openai.com/v1/chat/completions"

    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """使用OpenAI API翻译文本"""
        try:
            # 构建提示词
            prompt = self._build_prompt(text, source_lang, target_lang)

            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': self.model,
                'messages': [
                    {'role': 'system', 'content': '你是一个专业的翻译助手。'},
                    {'role': 'user', 'content': prompt}
                ],
                'temperature': 0.3,
                'max_tokens': 2000
            }

            async with self.session.post(self.base_url, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    translated_text = result['choices'][0]['message']['content'].strip()

                    return TranslationResult(
                        original_text=text,
                        translated_text=translated_text,
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='openai',
                        confidence=0.9
                    )
                else:
                    error_msg = f"OpenAI API错误: {response.status}"
                    return TranslationResult(
                        original_text=text,
                        translated_text="",
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='openai',
                        error=error_msg
                    )

        except Exception as e:
            self.logger.error(f"OpenAI翻译失败: {e}")
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider='openai',
                error=str(e)
            )

    def _build_prompt(self, text: str, source_lang: str, target_lang: str) -> str:
        """构建翻译提示词"""
        lang_map = {
            'zh-CN': '中文',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }

        target_lang_name = lang_map.get(target_lang, target_lang)

        return f"""请将以下文本翻译成{target_lang_name}，保持原文的格式和语义：

{text}

翻译要求：
1. 准确传达原文意思
2. 保持专业术语的准确性
3. 保持原文格式
4. 语言自然流畅

请只返回翻译结果，不要包含其他说明。"""

    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言"""
        return {
            'auto': '自动检测',
            'zh-CN': '中文(简体)',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }

class TranslationEngine:
    """翻译引擎管理器"""

    def __init__(self, config):
        self.config = config
        self.translators = {}
        self.logger = logging.getLogger(__name__)
        self._init_translators()

    def _init_translators(self):
        """初始化翻译器"""
        # 百度翻译
        baidu_app_id = self.config.get_api_key('baidu_app_id')
        baidu_secret = self.config.get_api_key('baidu_secret')
        if baidu_app_id and baidu_secret:
            self.translators['baidu'] = BaiduTranslator(baidu_app_id, baidu_secret)

        # 有道翻译
        youdao_key = self.config.get_api_key('youdao_key')
        youdao_secret = self.config.get_api_key('youdao_secret')
        if youdao_key and youdao_secret:
            self.translators['youdao'] = YoudaoTranslator(youdao_key, youdao_secret)

        # 腾讯翻译
        tencent_id = self.config.get_api_key('tencent_id')
        tencent_key = self.config.get_api_key('tencent_key')
        if tencent_id and tencent_key:
            self.translators['tencent'] = TencentTranslator(tencent_id, tencent_key)

        # 通义千问
        qwen_key = self.config.get_api_key('qwen')
        if qwen_key:
            self.translators['qwen'] = QwenTranslator(qwen_key)

        # DeepSeek
        deepseek_key = self.config.get_api_key('deepseek')
        if deepseek_key:
            self.translators['deepseek'] = DeepSeekTranslator(deepseek_key)

        # 豆包
        doubao_key = self.config.get_api_key('doubao')
        if doubao_key:
            self.translators['doubao'] = DoubaoTranslator(doubao_key)

        # OpenAI翻译
        openai_key = self.config.get_api_key('openai')
        if openai_key:
            self.translators['openai'] = OpenAITranslator(openai_key)

        # Google翻译（免费版本，总是可用）
        google_key = self.config.get_api_key('google')
        self.translators['google'] = GoogleTranslator(google_key)

    async def translate_text(self, text: str, source_lang: str = 'auto',
                           target_lang: str = 'zh-CN', provider: str = 'google') -> TranslationResult:
        """翻译单个文本"""
        if provider not in self.translators:
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider=provider,
                error=f"不支持的翻译提供商: {provider}"
            )

        translator = self.translators[provider]
        async with translator:
            return await translator.translate(text, source_lang, target_lang)

    async def translate_batch(self, texts: List[str], source_lang: str = 'auto',
                            target_lang: str = 'zh-CN', provider: str = 'google',
                            max_concurrent: int = 5) -> List[TranslationResult]:
        """批量翻译文本"""
        semaphore = asyncio.Semaphore(max_concurrent)

        async def translate_with_semaphore(text):
            async with semaphore:
                return await self.translate_text(text, source_lang, target_lang, provider)

        tasks = [translate_with_semaphore(text) for text in texts]
        return await asyncio.gather(*tasks)

    def get_available_providers(self) -> List[str]:
        """获取可用的翻译提供商"""
        return list(self.translators.keys())

    def get_supported_languages(self, provider: str) -> Dict[str, str]:
        """获取指定提供商支持的语言"""
        if provider in self.translators:
            return self.translators[provider].get_supported_languages()
        return {}

class BaiduTranslator(BaseTranslator):
    """百度翻译"""

    def __init__(self, app_id: str, secret_key: str):
        super().__init__()
        self.app_id = app_id
        self.secret_key = secret_key
        self.base_url = "https://fanyi-api.baidu.com/api/trans/vip/translate"

    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """使用百度翻译API"""
        import hashlib
        import random
        import time

        try:
            salt = str(random.randint(32768, 65536))
            sign_str = self.app_id + text + salt + self.secret_key
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()

            params = {
                'q': text,
                'from': self._convert_lang_code(source_lang),
                'to': self._convert_lang_code(target_lang),
                'appid': self.app_id,
                'salt': salt,
                'sign': sign
            }

            async with self.session.get(self.base_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'trans_result' in data:
                        translated_text = data['trans_result'][0]['dst']
                        return TranslationResult(
                            original_text=text,
                            translated_text=translated_text,
                            source_language=source_lang,
                            target_language=target_lang,
                            provider='baidu',
                            confidence=0.85
                        )
                    else:
                        error_msg = data.get('error_msg', '翻译失败')
                        return TranslationResult(
                            original_text=text,
                            translated_text="",
                            source_language=source_lang,
                            target_language=target_lang,
                            provider='baidu',
                            error=error_msg
                        )
        except Exception as e:
            self.logger.error(f"百度翻译失败: {e}")
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider='baidu',
                error=str(e)
            )

    def _convert_lang_code(self, lang_code: str) -> str:
        """转换语言代码"""
        lang_map = {
            'auto': 'auto',
            'zh-CN': 'zh',
            'en': 'en',
            'ja': 'jp',
            'ko': 'kor',
            'fr': 'fra',
            'de': 'de',
            'es': 'spa',
            'ru': 'ru'
        }
        return lang_map.get(lang_code, lang_code)

    def get_supported_languages(self) -> Dict[str, str]:
        return {
            'auto': '自动检测',
            'zh-CN': '中文',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }

class YoudaoTranslator(BaseTranslator):
    """有道翻译"""

    def __init__(self, app_key: str, app_secret: str):
        super().__init__()
        self.app_key = app_key
        self.app_secret = app_secret
        self.base_url = "https://openapi.youdao.com/api"

    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """使用有道翻译API"""
        import hashlib
        import time
        import uuid

        try:
            curtime = str(int(time.time()))
            salt = str(uuid.uuid1())
            sign_str = self.app_key + self._truncate(text) + salt + curtime + self.app_secret
            sign = hashlib.sha256(sign_str.encode('utf-8')).hexdigest()

            data = {
                'q': text,
                'from': self._convert_lang_code(source_lang),
                'to': self._convert_lang_code(target_lang),
                'appKey': self.app_key,
                'salt': salt,
                'sign': sign,
                'signType': 'v3',
                'curtime': curtime
            }

            async with self.session.post(self.base_url, data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('errorCode') == '0':
                        translated_text = result['translation'][0]
                        return TranslationResult(
                            original_text=text,
                            translated_text=translated_text,
                            source_language=source_lang,
                            target_language=target_lang,
                            provider='youdao',
                            confidence=0.85
                        )
                    else:
                        error_msg = f"有道翻译错误: {result.get('errorCode')}"
                        return TranslationResult(
                            original_text=text,
                            translated_text="",
                            source_language=source_lang,
                            target_language=target_lang,
                            provider='youdao',
                            error=error_msg
                        )
        except Exception as e:
            self.logger.error(f"有道翻译失败: {e}")
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider='youdao',
                error=str(e)
            )

    def _truncate(self, q: str) -> str:
        """截断文本用于签名"""
        if q is None:
            return None
        size = len(q)
        return q if size <= 20 else q[0:10] + str(size) + q[size-10:size]

    def _convert_lang_code(self, lang_code: str) -> str:
        """转换语言代码"""
        lang_map = {
            'auto': 'auto',
            'zh-CN': 'zh-CHS',
            'en': 'en',
            'ja': 'ja',
            'ko': 'ko',
            'fr': 'fr',
            'de': 'de',
            'es': 'es',
            'ru': 'ru'
        }
        return lang_map.get(lang_code, lang_code)

    def get_supported_languages(self) -> Dict[str, str]:
        return {
            'auto': '自动检测',
            'zh-CN': '中文',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }

class TencentTranslator(BaseTranslator):
    """腾讯翻译"""

    def __init__(self, secret_id: str, secret_key: str, region: str = "ap-beijing"):
        super().__init__()
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.region = region
        self.base_url = "https://tmt.tencentcloudapi.com"

    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """使用腾讯翻译API"""
        import hashlib
        import hmac
        import json
        import time

        try:
            # 腾讯云API签名算法
            service = "tmt"
            host = "tmt.tencentcloudapi.com"
            algorithm = "TC3-HMAC-SHA256"
            timestamp = int(time.time())
            date = time.strftime('%Y-%m-%d', time.gmtime(timestamp))

            # 构建请求
            payload = {
                "SourceText": text,
                "Source": self._convert_lang_code(source_lang),
                "Target": self._convert_lang_code(target_lang),
                "ProjectId": 0
            }

            headers = {
                "Authorization": self._get_authorization(payload, timestamp, date),
                "Content-Type": "application/json; charset=utf-8",
                "Host": host,
                "X-TC-Action": "TextTranslate",
                "X-TC-Timestamp": str(timestamp),
                "X-TC-Version": "2018-03-21",
                "X-TC-Region": self.region
            }

            async with self.session.post(
                self.base_url,
                headers=headers,
                json=payload
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if 'Response' in result and 'TargetText' in result['Response']:
                        translated_text = result['Response']['TargetText']
                        return TranslationResult(
                            original_text=text,
                            translated_text=translated_text,
                            source_language=source_lang,
                            target_language=target_lang,
                            provider='tencent',
                            confidence=0.85
                        )
                    else:
                        error_msg = result.get('Response', {}).get('Error', {}).get('Message', '翻译失败')
                        return TranslationResult(
                            original_text=text,
                            translated_text="",
                            source_language=source_lang,
                            target_language=target_lang,
                            provider='tencent',
                            error=error_msg
                        )
        except Exception as e:
            self.logger.error(f"腾讯翻译失败: {e}")
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider='tencent',
                error=str(e)
            )

    def _get_authorization(self, payload: dict, timestamp: int, date: str) -> str:
        """生成腾讯云API授权"""
        # 这里简化实现，实际使用时需要完整的签名算法
        return f"TC3-HMAC-SHA256 Credential={self.secret_id}/{date}/tmt/tc3_request"

    def _convert_lang_code(self, lang_code: str) -> str:
        """转换语言代码"""
        lang_map = {
            'auto': 'auto',
            'zh-CN': 'zh',
            'en': 'en',
            'ja': 'ja',
            'ko': 'ko',
            'fr': 'fr',
            'de': 'de',
            'es': 'es',
            'ru': 'ru'
        }
        return lang_map.get(lang_code, lang_code)

    def get_supported_languages(self) -> Dict[str, str]:
        return {
            'auto': '自动检测',
            'zh-CN': '中文',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }

class QwenTranslator(BaseTranslator):
    """通义千问翻译"""

    def __init__(self, api_key: str, model: str = "qwen-turbo"):
        super().__init__(api_key)
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """使用通义千问翻译"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            prompt = self._build_prompt(text, source_lang, target_lang)

            data = {
                'model': self.model,
                'input': {
                    'messages': [
                        {'role': 'system', 'content': '你是一个专业的翻译助手。'},
                        {'role': 'user', 'content': prompt}
                    ]
                },
                'parameters': {
                    'temperature': 0.3,
                    'max_tokens': 2000
                }
            }

            async with self.session.post(self.base_url, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    translated_text = result['output']['choices'][0]['message']['content'].strip()

                    return TranslationResult(
                        original_text=text,
                        translated_text=translated_text,
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='qwen',
                        confidence=0.9
                    )
                else:
                    error_msg = f"通义千问API错误: {response.status}"
                    return TranslationResult(
                        original_text=text,
                        translated_text="",
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='qwen',
                        error=error_msg
                    )
        except Exception as e:
            self.logger.error(f"通义千问翻译失败: {e}")
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider='qwen',
                error=str(e)
            )

    def _build_prompt(self, text: str, source_lang: str, target_lang: str) -> str:
        """构建翻译提示词"""
        lang_map = {
            'zh-CN': '中文',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }

        target_lang_name = lang_map.get(target_lang, target_lang)

        return f"""请将以下文本翻译成{target_lang_name}，要求：
1. 准确传达原文意思
2. 保持专业术语的准确性
3. 语言自然流畅
4. 只返回翻译结果，不要其他说明

原文：
{text}

翻译："""

    def get_supported_languages(self) -> Dict[str, str]:
        return {
            'auto': '自动检测',
            'zh-CN': '中文',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }

class DeepSeekTranslator(BaseTranslator):
    """DeepSeek翻译"""

    def __init__(self, api_key: str, model: str = "deepseek-chat"):
        super().__init__(api_key)
        self.model = model
        self.base_url = "https://api.deepseek.com/v1/chat/completions"

    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """使用DeepSeek翻译"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            prompt = self._build_prompt(text, source_lang, target_lang)

            data = {
                'model': self.model,
                'messages': [
                    {'role': 'system', 'content': '你是一个专业的翻译助手。'},
                    {'role': 'user', 'content': prompt}
                ],
                'temperature': 0.3,
                'max_tokens': 2000
            }

            async with self.session.post(self.base_url, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    translated_text = result['choices'][0]['message']['content'].strip()

                    return TranslationResult(
                        original_text=text,
                        translated_text=translated_text,
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='deepseek',
                        confidence=0.9
                    )
                else:
                    error_msg = f"DeepSeek API错误: {response.status}"
                    return TranslationResult(
                        original_text=text,
                        translated_text="",
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='deepseek',
                        error=error_msg
                    )
        except Exception as e:
            self.logger.error(f"DeepSeek翻译失败: {e}")
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider='deepseek',
                error=str(e)
            )

    def _build_prompt(self, text: str, source_lang: str, target_lang: str) -> str:
        """构建翻译提示词"""
        lang_map = {
            'zh-CN': '中文',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }

        target_lang_name = lang_map.get(target_lang, target_lang)

        return f"""请将以下文本翻译成{target_lang_name}，要求：
1. 准确传达原文意思
2. 保持专业术语的准确性
3. 语言自然流畅
4. 只返回翻译结果

原文：{text}"""

    def get_supported_languages(self) -> Dict[str, str]:
        return {
            'auto': '自动检测',
            'zh-CN': '中文',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }

class DoubaoTranslator(BaseTranslator):
    """豆包翻译（字节跳动）"""

    def __init__(self, api_key: str, model: str = "doubao-lite-4k"):
        super().__init__(api_key)
        self.model = model
        self.base_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"

    async def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """使用豆包翻译"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            prompt = self._build_prompt(text, source_lang, target_lang)

            data = {
                'model': self.model,
                'messages': [
                    {'role': 'system', 'content': '你是一个专业的翻译助手。'},
                    {'role': 'user', 'content': prompt}
                ],
                'temperature': 0.3,
                'max_tokens': 2000
            }

            async with self.session.post(self.base_url, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    translated_text = result['choices'][0]['message']['content'].strip()

                    return TranslationResult(
                        original_text=text,
                        translated_text=translated_text,
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='doubao',
                        confidence=0.9
                    )
                else:
                    error_msg = f"豆包API错误: {response.status}"
                    return TranslationResult(
                        original_text=text,
                        translated_text="",
                        source_language=source_lang,
                        target_language=target_lang,
                        provider='doubao',
                        error=error_msg
                    )
        except Exception as e:
            self.logger.error(f"豆包翻译失败: {e}")
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                provider='doubao',
                error=str(e)
            )

    def _build_prompt(self, text: str, source_lang: str, target_lang: str) -> str:
        """构建翻译提示词"""
        lang_map = {
            'zh-CN': '中文',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }

        target_lang_name = lang_map.get(target_lang, target_lang)

        return f"""请将以下文本翻译成{target_lang_name}，要求：
1. 准确传达原文意思
2. 保持专业术语的准确性
3. 语言自然流畅
4. 只返回翻译结果

原文：{text}"""

    def get_supported_languages(self) -> Dict[str, str]:
        return {
            'auto': '自动检测',
            'zh-CN': '中文',
            'en': '英语',
            'ja': '日语',
            'ko': '韩语',
            'fr': '法语',
            'de': '德语',
            'es': '西班牙语',
            'ru': '俄语'
        }


