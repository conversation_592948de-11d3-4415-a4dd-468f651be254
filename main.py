#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Document Translator - 主程序入口
一个功能完整的桌面端文档翻译工具
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.ui.main_window import DocumentTranslatorApp
from src.core.config import Config
from src.utils.logger import setup_logger

def main():
    """主程序入口"""
    try:
        # 设置日志
        setup_logger()
        logger = logging.getLogger(__name__)
        logger.info("启动文档翻译工具...")
        
        # 初始化配置
        config = Config()
        
        # 启动应用
        app = DocumentTranslatorApp(config)
        app.run()
        
    except Exception as e:
        logging.error(f"程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
