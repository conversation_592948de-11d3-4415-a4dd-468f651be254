#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面模块
使用Gradio构建用户界面
"""

import gradio as gr
import asyncio
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import tempfile
import os

from ..core.config import Config
from ..core.translator import TranslationEngine
from ..document.parser import DocumentProcessor
from ..document.exporter import DocumentExporter
from ..utils.logger import setup_logger

class DocumentTranslatorApp:
    """文档翻译应用主界面"""
    
    def __init__(self, config: Config):
        self.config = config
        self.translation_engine = TranslationEngine(config)
        self.document_processor = DocumentProcessor(config)
        self.document_exporter = DocumentExporter(config)
        self.logger = logging.getLogger(__name__)
        
        # 状态变量
        self.current_document = None
        self.translated_document = None
        self.translation_progress = 0
        
        # 创建界面
        self.interface = self._create_interface()
    
    def _create_interface(self) -> gr.Blocks:
        """创建Gradio界面"""
        with gr.Blocks(
            title="文档翻译工具",
            theme=gr.themes.Soft(),
            css=self._get_custom_css()
        ) as interface:
            
            # 标题
            gr.Markdown("# 📄 智能文档翻译工具")
            gr.Markdown("支持多种文档格式，提供专业的翻译服务")
            
            with gr.Tabs() as tabs:
                # 文本翻译标签页
                with gr.TabItem("📝 文本翻译", id="text_translation"):
                    self._create_text_translation_tab()
                
                # 文档翻译标签页
                with gr.TabItem("📄 文档翻译", id="document_translation"):
                    self._create_document_translation_tab()
                
                # 设置标签页
                with gr.TabItem("⚙️ 设置", id="settings"):
                    self._create_settings_tab()
                
                # 帮助标签页
                with gr.TabItem("❓ 帮助", id="help"):
                    self._create_help_tab()
        
        return interface
    
    def _create_text_translation_tab(self):
        """创建文本翻译标签页"""
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 翻译设置")
                
                source_lang = gr.Dropdown(
                    choices=list(self._get_language_choices().items()),
                    value="auto",
                    label="源语言",
                    info="选择要翻译的源语言"
                )
                
                target_lang = gr.Dropdown(
                    choices=list(self._get_language_choices().items()),
                    value="zh-CN",
                    label="目标语言",
                    info="选择翻译目标语言"
                )
                
                provider = gr.Dropdown(
                    choices=self._get_provider_choices(),
                    value=self.config.translation.default_provider,
                    label="翻译服务",
                    info="选择翻译服务提供商"
                )
            
            with gr.Column(scale=2):
                gr.Markdown("### 文本翻译")
                
                input_text = gr.Textbox(
                    label="输入文本",
                    placeholder="请输入要翻译的文本...",
                    lines=8,
                    max_lines=15
                )
                
                with gr.Row():
                    translate_btn = gr.Button("🔄 翻译", variant="primary")
                    clear_btn = gr.Button("🗑️ 清空")
                
                output_text = gr.Textbox(
                    label="翻译结果",
                    lines=8,
                    max_lines=15,
                    interactive=False
                )
                
                translation_info = gr.Textbox(
                    label="翻译信息",
                    lines=2,
                    interactive=False,
                    visible=False
                )
        
        # 事件绑定
        translate_btn.click(
            fn=self._translate_text,
            inputs=[input_text, source_lang, target_lang, provider],
            outputs=[output_text, translation_info]
        )
        
        clear_btn.click(
            fn=lambda: ("", "", ""),
            outputs=[input_text, output_text, translation_info]
        )
    
    def _create_document_translation_tab(self):
        """创建文档翻译标签页"""
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 文档上传")
                
                file_upload = gr.File(
                    label="选择文档",
                    file_types=self.document_processor.parser_factory.get_supported_formats(),
                    type="filepath"
                )
                
                gr.Markdown("### 翻译设置")
                
                doc_source_lang = gr.Dropdown(
                    choices=list(self._get_language_choices().items()),
                    value="auto",
                    label="源语言"
                )
                
                doc_target_lang = gr.Dropdown(
                    choices=list(self._get_language_choices().items()),
                    value="zh-CN",
                    label="目标语言"
                )
                
                doc_provider = gr.Dropdown(
                    choices=self._get_provider_choices(),
                    value=self.config.translation.default_provider,
                    label="翻译服务"
                )
                
                with gr.Row():
                    parse_btn = gr.Button("📖 解析文档", variant="secondary")
                    translate_doc_btn = gr.Button("🔄 翻译文档", variant="primary")
                
                # 文档信息
                doc_info = gr.JSON(
                    label="文档信息",
                    visible=False
                )
            
            with gr.Column(scale=2):
                gr.Markdown("### 文档预览与翻译结果")
                
                with gr.Tabs():
                    with gr.TabItem("原文预览"):
                        original_preview = gr.HTML(
                            label="原文预览",
                            value="<p>请先上传并解析文档</p>"
                        )
                    
                    with gr.TabItem("翻译结果"):
                        translated_preview = gr.HTML(
                            label="翻译结果",
                            value="<p>请先翻译文档</p>"
                        )
                
                # 进度条
                progress_bar = gr.Progress()
                
                # 导出设置
                with gr.Row():
                    export_format = gr.Dropdown(
                        choices=[".pdf", ".docx", ".txt", ".md"],
                        value=".pdf",
                        label="导出格式"
                    )
                    
                    export_mode = gr.Dropdown(
                        choices=[
                            ("仅翻译结果", "translated_only"),
                            ("双语对照", "bilingual"),
                            ("仅原文", "original_only")
                        ],
                        value="translated_only",
                        label="导出模式"
                    )
                
                export_btn = gr.Button("💾 导出文档", variant="secondary")
                download_file = gr.File(label="下载文件", visible=False)
        
        # 事件绑定
        parse_btn.click(
            fn=self._parse_document,
            inputs=[file_upload],
            outputs=[doc_info, original_preview]
        )
        
        translate_doc_btn.click(
            fn=self._translate_document,
            inputs=[doc_source_lang, doc_target_lang, doc_provider],
            outputs=[translated_preview],
            show_progress=True
        )
        
        export_btn.click(
            fn=self._export_document,
            inputs=[export_format, export_mode],
            outputs=[download_file]
        )
    
    def _create_settings_tab(self):
        """创建设置标签页"""
        with gr.Row():
            with gr.Column():
                gr.Markdown("### API 密钥设置")
                
                with gr.Accordion("Google 翻译", open=False):
                    google_key = gr.Textbox(
                        label="Google API Key",
                        type="password",
                        value=self.config.get_api_key("google") or ""
                    )
                    google_save_btn = gr.Button("保存 Google API Key")
                
                with gr.Accordion("OpenAI", open=False):
                    openai_key = gr.Textbox(
                        label="OpenAI API Key",
                        type="password",
                        value=self.config.get_api_key("openai") or ""
                    )
                    openai_save_btn = gr.Button("保存 OpenAI API Key")
                
                with gr.Accordion("Azure 翻译", open=False):
                    azure_key = gr.Textbox(
                        label="Azure API Key",
                        type="password",
                        value=self.config.get_api_key("azure") or ""
                    )
                    azure_endpoint = gr.Textbox(
                        label="Azure Endpoint",
                        value=self.config.get_api_key("azure_endpoint") or ""
                    )
                    azure_save_btn = gr.Button("保存 Azure 设置")
            
            with gr.Column():
                gr.Markdown("### 翻译设置")
                
                default_provider_setting = gr.Dropdown(
                    choices=self._get_provider_choices(),
                    value=self.config.translation.default_provider,
                    label="默认翻译服务"
                )
                
                max_concurrent_setting = gr.Slider(
                    minimum=1,
                    maximum=10,
                    value=self.config.translation.max_concurrent,
                    step=1,
                    label="最大并发数"
                )
                
                timeout_setting = gr.Slider(
                    minimum=10,
                    maximum=120,
                    value=self.config.translation.timeout,
                    step=5,
                    label="超时时间(秒)"
                )
                
                gr.Markdown("### 文档设置")
                
                max_file_size_setting = gr.Slider(
                    minimum=10,
                    maximum=500,
                    value=self.config.document.max_file_size,
                    step=10,
                    label="最大文件大小(MB)"
                )
                
                preserve_layout_setting = gr.Checkbox(
                    value=self.config.document.preserve_layout,
                    label="保持文档布局"
                )
                
                save_settings_btn = gr.Button("💾 保存设置", variant="primary")
        
        # 事件绑定
        google_save_btn.click(
            fn=lambda key: self._save_api_key("google", key),
            inputs=[google_key],
            outputs=[]
        )
        
        openai_save_btn.click(
            fn=lambda key: self._save_api_key("openai", key),
            inputs=[openai_key],
            outputs=[]
        )
        
        azure_save_btn.click(
            fn=lambda key, endpoint: self._save_azure_settings(key, endpoint),
            inputs=[azure_key, azure_endpoint],
            outputs=[]
        )
        
        save_settings_btn.click(
            fn=self._save_general_settings,
            inputs=[
                default_provider_setting,
                max_concurrent_setting,
                timeout_setting,
                max_file_size_setting,
                preserve_layout_setting
            ],
            outputs=[]
        )
    
    def _create_help_tab(self):
        """创建帮助标签页"""
        help_content = """
        ## 📖 使用指南
        
        ### 文本翻译
        1. 在"文本翻译"标签页中输入要翻译的文本
        2. 选择源语言和目标语言
        3. 选择翻译服务提供商
        4. 点击"翻译"按钮获取结果
        
        ### 文档翻译
        1. 在"文档翻译"标签页中上传文档文件
        2. 点击"解析文档"按钮解析文档内容
        3. 设置翻译参数
        4. 点击"翻译文档"按钮开始翻译
        5. 在预览区查看翻译结果
        6. 选择导出格式和模式，点击"导出文档"
        
        ### 支持的文档格式
        - PDF (.pdf)
        - Word文档 (.docx)
        - 文本文件 (.txt)
        - Markdown文件 (.md)
        
        ### 翻译服务提供商
        - **Google翻译**: 支持多种语言，质量稳定
        - **OpenAI**: 基于大语言模型，适合专业文档
        - **Azure翻译**: 微软提供的翻译服务
        
        ### 设置说明
        - 在"设置"标签页中配置API密钥
        - 调整翻译参数以获得最佳效果
        - 设置文档处理选项
        
        ### 注意事项
        - 请确保网络连接正常
        - 大文件翻译可能需要较长时间
        - 建议先测试小文档确认效果
        """
        
        gr.Markdown(help_content)
    
    def _get_custom_css(self) -> str:
        """获取自定义CSS样式"""
        return """
        .gradio-container {
            max-width: 1200px !important;
        }
        
        .tab-nav {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        """
    
    def _get_language_choices(self) -> Dict[str, str]:
        """获取语言选择"""
        return {
            "自动检测": "auto",
            "中文(简体)": "zh-CN",
            "中文(繁体)": "zh-TW",
            "英语": "en",
            "日语": "ja",
            "韩语": "ko",
            "法语": "fr",
            "德语": "de",
            "西班牙语": "es",
            "俄语": "ru",
            "阿拉伯语": "ar"
        }
    
    def _get_provider_choices(self) -> List[Tuple[str, str]]:
        """获取翻译服务提供商选择"""
        providers = []
        available_providers = self.translation_engine.get_available_providers()
        
        provider_names = {
            "google": "Google 翻译",
            "openai": "OpenAI",
            "azure": "Azure 翻译"
        }
        
        for provider in available_providers:
            name = provider_names.get(provider, provider.title())
            providers.append((name, provider))
        
        return providers
    
    async def _translate_text(self, text: str, source_lang: str, target_lang: str, provider: str) -> Tuple[str, str]:
        """翻译文本"""
        try:
            if not text.strip():
                return "", ""
            
            result = await self.translation_engine.translate_text(
                text, source_lang, target_lang, provider
            )
            
            if result.error:
                return f"翻译失败: {result.error}", f"提供商: {result.provider}"
            
            info = f"提供商: {result.provider} | 源语言: {result.source_language} | 置信度: {result.confidence:.2f}"
            return result.translated_text, info
            
        except Exception as e:
            self.logger.error(f"文本翻译失败: {e}")
            return f"翻译失败: {str(e)}", ""
    
    def _parse_document(self, file_path: str) -> Tuple[Dict, str]:
        """解析文档"""
        try:
            if not file_path:
                return {}, "<p>请先上传文档</p>"
            
            # 处理文档
            self.current_document = self.document_processor.process_document(file_path)
            
            # 生成预览HTML
            preview_html = self._generate_document_preview(self.current_document)
            
            # 返回文档信息
            doc_info = {
                "文件名": self.current_document['file_name'],
                "页数": self.current_document['stats']['total_pages'],
                "文本元素": self.current_document['stats']['text_elements'],
                "图像元素": self.current_document['stats']['image_elements'],
                "表格元素": self.current_document['stats']['table_elements'],
                "总字符数": self.current_document['stats']['total_characters'],
                "估计单词数": self.current_document['stats']['estimated_words']
            }
            
            return doc_info, preview_html
            
        except Exception as e:
            self.logger.error(f"文档解析失败: {e}")
            return {"错误": str(e)}, f"<p>文档解析失败: {str(e)}</p>"
    
    async def _translate_document(self, source_lang: str, target_lang: str, provider: str) -> str:
        """翻译文档"""
        try:
            if not self.current_document:
                return "<p>请先解析文档</p>"
            
            # 提取所有文本内容
            all_texts = []
            text_mapping = []  # 记录文本在页面中的位置
            
            for page_idx, page in enumerate(self.current_document['pages']):
                for elem_idx, element in enumerate(page.elements):
                    if element.element_type == "text" and element.content.strip():
                        all_texts.append(element.content)
                        text_mapping.append((page_idx, elem_idx))
            
            if not all_texts:
                return "<p>文档中没有找到可翻译的文本</p>"
            
            # 批量翻译
            translation_results = await self.translation_engine.translate_batch(
                all_texts, source_lang, target_lang, provider,
                max_concurrent=self.config.translation.max_concurrent
            )
            
            # 创建翻译后的文档页面
            translated_pages = []
            result_idx = 0
            
            for page in self.current_document['pages']:
                translated_elements = []
                
                for element in page.elements:
                    if element.element_type == "text" and element.content.strip():
                        if result_idx < len(translation_results):
                            result = translation_results[result_idx]
                            if not result.error:
                                # 创建翻译后的元素
                                translated_element = DocumentElement(
                                    element_type=element.element_type,
                                    content=result.translated_text,
                                    position=element.position,
                                    page_number=element.page_number,
                                    font_info=element.font_info,
                                    style_info=element.style_info
                                )
                                translated_elements.append(translated_element)
                            else:
                                # 翻译失败，保留原文
                                translated_elements.append(element)
                            result_idx += 1
                    else:
                        # 非文本元素保持不变
                        translated_elements.append(element)
                
                translated_page = DocumentPage(
                    page_number=page.page_number,
                    elements=translated_elements,
                    width=page.width,
                    height=page.height
                )
                translated_pages.append(translated_page)
            
            # 保存翻译结果
            self.translated_document = {
                'pages': translated_pages,
                'stats': self.current_document['stats'],
                'file_name': self.current_document['file_name'],
                'translation_info': {
                    'source_lang': source_lang,
                    'target_lang': target_lang,
                    'provider': provider,
                    'total_texts': len(all_texts),
                    'successful_translations': sum(1 for r in translation_results if not r.error)
                }
            }
            
            # 生成翻译预览
            preview_html = self._generate_translation_preview(self.translated_document)
            
            return preview_html
            
        except Exception as e:
            self.logger.error(f"文档翻译失败: {e}")
            return f"<p>文档翻译失败: {str(e)}</p>"
    
    def _export_document(self, export_format: str, export_mode: str) -> Optional[str]:
        """导出文档"""
        try:
            if not self.current_document:
                gr.Warning("请先解析文档")
                return None
            
            if export_mode in ["translated_only", "bilingual"] and not self.translated_document:
                gr.Warning("请先翻译文档")
                return None
            
            # 创建临时文件
            temp_dir = self.config.get_temp_dir()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"translated_document_{timestamp}{export_format}"
            output_path = temp_dir / filename
            
            # 确定要导出的页面
            if export_mode == "original_only":
                pages_to_export = self.current_document['pages']
            elif export_mode == "translated_only":
                pages_to_export = self.translated_document['pages']
            else:  # bilingual
                pages_to_export = None  # 将在导出器中处理
            
            # 导出文档
            if export_mode == "bilingual":
                success = self.document_exporter.export_translated_document(
                    self.current_document['pages'],
                    self.translated_document['pages'],
                    str(output_path),
                    export_mode,
                    title=f"翻译文档 - {self.current_document['file_name']}"
                )
            else:
                success = self.document_exporter.exporter_factory.export_document(
                    pages_to_export,
                    str(output_path),
                    title=f"翻译文档 - {self.current_document['file_name']}"
                )
            
            if success:
                gr.Info("文档导出成功")
                return str(output_path)
            else:
                gr.Error("文档导出失败")
                return None
                
        except Exception as e:
            self.logger.error(f"文档导出失败: {e}")
            gr.Error(f"文档导出失败: {str(e)}")
            return None
    
    def _generate_document_preview(self, document: Dict) -> str:
        """生成文档预览HTML"""
        html_content = ["<div style='font-family: Arial, sans-serif; line-height: 1.6;'>"]
        
        for page in document['pages']:
            html_content.append(f"<h3>第 {page.page_number} 页</h3>")
            html_content.append("<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>")
            
            for element in page.elements:
                if element.element_type == "text":
                    html_content.append(f"<p>{element.content}</p>")
                elif element.element_type == "table":
                    html_content.append("<div style='background: #f5f5f5; padding: 5px; margin: 5px 0;'>")
                    html_content.append("<strong>表格内容:</strong><br>")
                    html_content.append(f"<pre>{element.content}</pre>")
                    html_content.append("</div>")
                elif element.element_type == "image":
                    html_content.append("<div style='background: #e8f4fd; padding: 5px; margin: 5px 0;'>")
                    html_content.append(f"<strong>图像:</strong> {element.content}")
                    html_content.append("</div>")
            
            html_content.append("</div>")
        
        html_content.append("</div>")
        return "".join(html_content)
    
    def _generate_translation_preview(self, translated_document: Dict) -> str:
        """生成翻译预览HTML"""
        html_content = ["<div style='font-family: Arial, sans-serif; line-height: 1.6;'>"]
        
        # 翻译信息
        info = translated_document['translation_info']
        html_content.append("<div style='background: #e8f5e8; padding: 10px; margin-bottom: 20px; border-radius: 5px;'>")
        html_content.append(f"<strong>翻译信息:</strong><br>")
        html_content.append(f"源语言: {info['source_lang']} → 目标语言: {info['target_lang']}<br>")
        html_content.append(f"翻译服务: {info['provider']}<br>")
        html_content.append(f"成功翻译: {info['successful_translations']}/{info['total_texts']} 个文本段落")
        html_content.append("</div>")
        
        # 翻译内容
        for page in translated_document['pages']:
            html_content.append(f"<h3>第 {page.page_number} 页</h3>")
            html_content.append("<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>")
            
            for element in page.elements:
                if element.element_type == "text":
                    html_content.append(f"<p>{element.content}</p>")
                elif element.element_type == "table":
                    html_content.append("<div style='background: #f5f5f5; padding: 5px; margin: 5px 0;'>")
                    html_content.append("<strong>表格内容:</strong><br>")
                    html_content.append(f"<pre>{element.content}</pre>")
                    html_content.append("</div>")
                elif element.element_type == "image":
                    html_content.append("<div style='background: #e8f4fd; padding: 5px; margin: 5px 0;'>")
                    html_content.append(f"<strong>图像:</strong> {element.content}")
                    html_content.append("</div>")
            
            html_content.append("</div>")
        
        html_content.append("</div>")
        return "".join(html_content)
    
    def _save_api_key(self, provider: str, key: str):
        """保存API密钥"""
        if key.strip():
            self.config.set_api_key(provider, key.strip())
            gr.Info(f"{provider.title()} API密钥已保存")
            # 重新初始化翻译引擎
            self.translation_engine = TranslationEngine(self.config)
        else:
            gr.Warning("API密钥不能为空")
    
    def _save_azure_settings(self, key: str, endpoint: str):
        """保存Azure设置"""
        if key.strip() and endpoint.strip():
            self.config.set_api_key("azure", key.strip())
            self.config.set_api_key("azure_endpoint", endpoint.strip())
            gr.Info("Azure设置已保存")
            # 重新初始化翻译引擎
            self.translation_engine = TranslationEngine(self.config)
        else:
            gr.Warning("Azure API密钥和端点都不能为空")
    
    def _save_general_settings(self, default_provider: str, max_concurrent: int, 
                             timeout: int, max_file_size: int, preserve_layout: bool):
        """保存通用设置"""
        try:
            self.config.translation.default_provider = default_provider
            self.config.translation.max_concurrent = max_concurrent
            self.config.translation.timeout = timeout
            self.config.document.max_file_size = max_file_size
            self.config.document.preserve_layout = preserve_layout
            
            self.config.save_config()
            gr.Info("设置已保存")
            
        except Exception as e:
            gr.Error(f"保存设置失败: {str(e)}")
    
    def run(self, **kwargs):
        """启动应用"""
        default_kwargs = {
            "server_name": "0.0.0.0",
            "server_port": 7860,
            "share": False,
            "debug": False
        }
        default_kwargs.update(kwargs)
        
        self.logger.info("启动Gradio界面...")
        self.interface.launch(**default_kwargs)

# 导入必要的模块
from datetime import datetime
from ..document.parser import DocumentElement, DocumentPage