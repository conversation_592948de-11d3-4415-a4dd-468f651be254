#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面模块
使用Gradio构建用户界面
"""

import gradio as gr
import asyncio
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import tempfile
import os

from ..core.config import Config
from ..core.translator import TranslationEngine
from ..document.parser import DocumentProcessor
from ..document.exporter import DocumentExporter
from ..utils.logger import setup_logger

class DocumentTranslatorApp:
    """文档翻译应用主界面"""

    def __init__(self, config: Config):
        self.config = config
        self.translation_engine = TranslationEngine(config)
        self.document_processor = DocumentProcessor(config)
        self.document_exporter = DocumentExporter(config)
        self.logger = logging.getLogger(__name__)

        # 状态变量
        self.current_document = None
        self.translated_document = None
        self.translation_progress = 0

        # 创建界面
        self.interface = self._create_interface()

    def _create_interface(self) -> gr.Blocks:
        """创建Gradio界面"""
        with gr.Blocks(
            title="文档翻译工具",
            theme=gr.themes.Soft(),
            css=self._get_custom_css()
        ) as interface:

            # 标题
            gr.Markdown("# 📄 智能文档翻译工具")
            gr.Markdown("支持多种文档格式，提供专业的翻译服务")

            with gr.Tabs() as tabs:
                # 文本翻译标签页
                with gr.TabItem("📝 文本翻译", id="text_translation"):
                    self._create_text_translation_tab()

                # 文档翻译标签页
                with gr.TabItem("📄 文档翻译", id="document_translation"):
                    self._create_document_translation_tab()

                # 设置标签页
                with gr.TabItem("⚙️ 设置", id="settings"):
                    self._create_settings_tab()

                # 帮助标签页
                with gr.TabItem("❓ 帮助", id="help"):
                    self._create_help_tab()

        return interface

    def _create_text_translation_tab(self):
        """创建文本翻译标签页"""
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 翻译设置")

                source_lang = gr.Dropdown(
                    choices=list(self._get_language_choices().items()),
                    value="auto",
                    label="源语言",
                    info="选择要翻译的源语言"
                )

                target_lang = gr.Dropdown(
                    choices=list(self._get_language_choices().items()),
                    value="zh-CN",
                    label="目标语言",
                    info="选择翻译目标语言"
                )

                provider = gr.Dropdown(
                    choices=self._get_provider_choices(),
                    value=self._get_default_provider(),
                    label="翻译服务",
                    info="选择翻译服务提供商"
                )

            with gr.Column(scale=2):
                gr.Markdown("### 文本翻译")

                input_text = gr.Textbox(
                    label="输入文本",
                    placeholder="请输入要翻译的文本...",
                    lines=8,
                    max_lines=15
                )

                with gr.Row():
                    translate_btn = gr.Button("🔄 翻译", variant="primary")
                    clear_btn = gr.Button("🗑️ 清空")

                output_text = gr.Textbox(
                    label="翻译结果",
                    lines=8,
                    max_lines=15,
                    interactive=False
                )

                translation_info = gr.Textbox(
                    label="翻译信息",
                    lines=2,
                    interactive=False,
                    visible=False
                )

        # 事件绑定
        translate_btn.click(
            fn=self._translate_text,
            inputs=[input_text, source_lang, target_lang, provider],
            outputs=[output_text, translation_info]
        )

        clear_btn.click(
            fn=lambda: ("", "", ""),
            outputs=[input_text, output_text, translation_info]
        )

    def _create_document_translation_tab(self):
        """创建文档翻译标签页"""
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 文档上传")

                file_upload = gr.File(
                    label="选择文档",
                    file_types=self.document_processor.parser_factory.get_supported_formats(),
                    type="filepath"
                )

                gr.Markdown("### 翻译设置")

                doc_source_lang = gr.Dropdown(
                    choices=list(self._get_language_choices().items()),
                    value="auto",
                    label="源语言"
                )

                doc_target_lang = gr.Dropdown(
                    choices=list(self._get_language_choices().items()),
                    value="zh-CN",
                    label="目标语言"
                )

                doc_provider = gr.Dropdown(
                    choices=self._get_provider_choices(),
                    value=self._get_default_provider(),
                    label="翻译服务"
                )

                with gr.Row():
                    parse_btn = gr.Button("📖 解析文档", variant="secondary")
                    translate_doc_btn = gr.Button("🔄 翻译文档", variant="primary")

                # 文档信息
                doc_info = gr.JSON(
                    label="文档信息",
                    visible=False
                )

            with gr.Column(scale=2):
                gr.Markdown("### 文档预览与翻译结果")

                with gr.Tabs():
                    with gr.TabItem("原文预览"):
                        original_preview = gr.HTML(
                            label="原文预览",
                            value="<p>请先上传并解析文档</p>"
                        )

                    with gr.TabItem("翻译结果"):
                        translated_preview = gr.HTML(
                            label="翻译结果",
                            value="<p>请先翻译文档</p>"
                        )

                # 进度条
                progress_bar = gr.Progress()

                # 导出设置
                with gr.Row():
                    export_format = gr.Dropdown(
                        choices=[".pdf", ".docx", ".txt", ".md"],
                        value=".pdf",
                        label="导出格式"
                    )

                    export_mode = gr.Dropdown(
                        choices=[
                            ("仅翻译结果", "translated_only"),
                            ("双语对照", "bilingual"),
                            ("仅原文", "original_only")
                        ],
                        value="translated_only",
                        label="导出模式"
                    )

                export_btn = gr.Button("💾 导出文档", variant="secondary")
                download_file = gr.File(label="下载文件", visible=False)

        # 事件绑定
        parse_btn.click(
            fn=self._parse_document,
            inputs=[file_upload],
            outputs=[doc_info, original_preview]
        )

        translate_doc_btn.click(
            fn=self._translate_document,
            inputs=[doc_source_lang, doc_target_lang, doc_provider],
            outputs=[translated_preview],
            show_progress=True
        )

        export_btn.click(
            fn=self._export_document,
            inputs=[export_format, export_mode],
            outputs=[download_file]
        )

    def _create_settings_tab(self):
        """创建设置标签页"""
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 🤖 AI翻译服务配置")

                with gr.Accordion("🔥 通义千问 (阿里云)", open=False):
                    with gr.Row():
                        qwen_key = gr.Textbox(
                            label="API Key",
                            type="password",
                            value=self.config.get_api_key("qwen") or "",
                            placeholder="请输入通义千问API Key"
                        )
                        qwen_save_btn = gr.Button("💾 保存", size="sm", variant="secondary")
                    gr.Markdown("💡 获取地址: [阿里云控制台](https://dashscope.console.aliyun.com/)")

                with gr.Accordion("🧠 DeepSeek", open=False):
                    with gr.Row():
                        deepseek_key = gr.Textbox(
                            label="API Key",
                            type="password",
                            value=self.config.get_api_key("deepseek") or "",
                            placeholder="请输入DeepSeek API Key"
                        )
                        deepseek_save_btn = gr.Button("💾 保存", size="sm", variant="secondary")
                    gr.Markdown("💡 获取地址: [DeepSeek平台](https://platform.deepseek.com/)")

                with gr.Accordion("🎯 豆包 (字节跳动)", open=False):
                    with gr.Row():
                        doubao_key = gr.Textbox(
                            label="API Key",
                            type="password",
                            value=self.config.get_api_key("doubao") or "",
                            placeholder="请输入豆包API Key"
                        )
                        doubao_save_btn = gr.Button("💾 保存", size="sm", variant="secondary")
                    gr.Markdown("💡 获取地址: [火山引擎控制台](https://console.volcengine.com/)")

                with gr.Accordion("🤖 OpenAI", open=False):
                    with gr.Row():
                        openai_key = gr.Textbox(
                            label="API Key",
                            type="password",
                            value=self.config.get_api_key("openai") or "",
                            placeholder="请输入OpenAI API Key"
                        )
                        openai_save_btn = gr.Button("💾 保存", size="sm", variant="secondary")
                    gr.Markdown("💡 获取地址: [OpenAI平台](https://platform.openai.com/)")

            with gr.Column():
                gr.Markdown("### 🌐 传统翻译服务配置")

                with gr.Accordion("🔵 百度翻译", open=False):
                    baidu_app_id = gr.Textbox(
                        label="APP ID",
                        value=self.config.get_api_key("baidu_app_id") or "",
                        placeholder="请输入百度翻译APP ID"
                    )
                    baidu_secret = gr.Textbox(
                        label="密钥",
                        type="password",
                        value=self.config.get_api_key("baidu_secret") or "",
                        placeholder="请输入百度翻译密钥"
                    )
                    baidu_save_btn = gr.Button("💾 保存百度配置", variant="secondary")
                    gr.Markdown("💡 获取地址: [百度翻译开放平台](https://fanyi-api.baidu.com/)")

                with gr.Accordion("📚 有道翻译", open=False):
                    youdao_key = gr.Textbox(
                        label="应用ID",
                        value=self.config.get_api_key("youdao_key") or "",
                        placeholder="请输入有道翻译应用ID"
                    )
                    youdao_secret = gr.Textbox(
                        label="应用密钥",
                        type="password",
                        value=self.config.get_api_key("youdao_secret") or "",
                        placeholder="请输入有道翻译应用密钥"
                    )
                    youdao_save_btn = gr.Button("💾 保存有道配置", variant="secondary")
                    gr.Markdown("💡 获取地址: [有道智云](https://ai.youdao.com/)")

                with gr.Accordion("🐧 腾讯翻译", open=False):
                    tencent_id = gr.Textbox(
                        label="SecretId",
                        value=self.config.get_api_key("tencent_id") or "",
                        placeholder="请输入腾讯云SecretId"
                    )
                    tencent_key = gr.Textbox(
                        label="SecretKey",
                        type="password",
                        value=self.config.get_api_key("tencent_key") or "",
                        placeholder="请输入腾讯云SecretKey"
                    )
                    tencent_save_btn = gr.Button("💾 保存腾讯配置", variant="secondary")
                    gr.Markdown("💡 获取地址: [腾讯云控制台](https://console.cloud.tencent.com/)")

        with gr.Row():
            with gr.Column():
                gr.Markdown("### ⚙️ 翻译参数设置")

                default_provider_setting = gr.Dropdown(
                    choices=self._get_provider_choices(),
                    value=self._get_default_provider(),
                    label="🎯 默认翻译服务",
                    info="选择默认使用的翻译服务"
                )

                max_concurrent_setting = gr.Slider(
                    minimum=1,
                    maximum=10,
                    value=self.config.translation.max_concurrent,
                    step=1,
                    label="🚀 最大并发数",
                    info="同时进行的翻译请求数量"
                )

                timeout_setting = gr.Slider(
                    minimum=10,
                    maximum=120,
                    value=self.config.translation.timeout,
                    step=5,
                    label="⏱️ 超时时间(秒)",
                    info="单个翻译请求的最大等待时间"
                )

            with gr.Column():
                gr.Markdown("### 📄 文档处理设置")

                max_file_size_setting = gr.Slider(
                    minimum=10,
                    maximum=500,
                    value=self.config.document.max_file_size,
                    step=10,
                    label="📦 最大文件大小(MB)",
                    info="支持上传的最大文件大小"
                )

                preserve_layout_setting = gr.Checkbox(
                    value=self.config.document.preserve_layout,
                    label="🎨 保持文档布局",
                    info="翻译时尽量保持原文档的格式和布局"
                )

                auto_detect_lang = gr.Checkbox(
                    value=True,
                    label="🔍 自动检测语言",
                    info="自动检测源文档的语言"
                )

        with gr.Row():
            save_settings_btn = gr.Button("💾 保存所有设置", variant="primary", size="lg")
            reset_settings_btn = gr.Button("🔄 重置设置", variant="secondary", size="lg")

        # 事件绑定
        qwen_save_btn.click(
            fn=lambda key: self._save_api_key("qwen", key),
            inputs=[qwen_key],
            outputs=[]
        )

        deepseek_save_btn.click(
            fn=lambda key: self._save_api_key("deepseek", key),
            inputs=[deepseek_key],
            outputs=[]
        )

        doubao_save_btn.click(
            fn=lambda key: self._save_api_key("doubao", key),
            inputs=[doubao_key],
            outputs=[]
        )

        openai_save_btn.click(
            fn=lambda key: self._save_api_key("openai", key),
            inputs=[openai_key],
            outputs=[]
        )

        baidu_save_btn.click(
            fn=lambda app_id, secret: self._save_baidu_settings(app_id, secret),
            inputs=[baidu_app_id, baidu_secret],
            outputs=[]
        )

        youdao_save_btn.click(
            fn=lambda key, secret: self._save_youdao_settings(key, secret),
            inputs=[youdao_key, youdao_secret],
            outputs=[]
        )

        tencent_save_btn.click(
            fn=lambda id, key: self._save_tencent_settings(id, key),
            inputs=[tencent_id, tencent_key],
            outputs=[]
        )

        save_settings_btn.click(
            fn=self._save_general_settings,
            inputs=[
                default_provider_setting,
                max_concurrent_setting,
                timeout_setting,
                max_file_size_setting,
                preserve_layout_setting
            ],
            outputs=[]
        )

    def _create_help_tab(self):
        """创建帮助标签页"""
        help_content = """
        ## 📖 使用指南

        ### 🔤 文本翻译
        1. 在"文本翻译"标签页中输入要翻译的文本
        2. 选择源语言和目标语言
        3. 选择翻译服务提供商
        4. 点击"翻译"按钮获取结果

        ### 📄 文档翻译
        1. 在"文档翻译"标签页中上传文档文件
        2. 点击"解析文档"按钮解析文档内容
        3. 设置翻译参数
        4. 点击"翻译文档"按钮开始翻译
        5. 在预览区查看翻译结果
        6. 选择导出格式和模式，点击"导出文档"

        ### 📁 支持的文档格式
        - **PDF** (.pdf) - 支持文本、图像、表格解析
        - **Word文档** (.docx) - 完整格式保持
        - **文本文件** (.txt) - 纯文本处理
        - **Markdown文件** (.md) - 格式化文本

        ### 🤖 AI翻译服务

        #### 🔥 通义千问 (推荐)
        - **优势**: 中文理解能力强，专业术语翻译准确
        - **适用**: 中英互译、技术文档、学术论文
        - **获取**: [阿里云控制台](https://dashscope.console.aliyun.com/)

        #### 🧠 DeepSeek
        - **优势**: 代码翻译能力强，逻辑性好
        - **适用**: 技术文档、API文档、代码注释
        - **获取**: [DeepSeek平台](https://platform.deepseek.com/)

        #### 🎯 豆包 (字节跳动)
        - **优势**: 响应速度快，多语言支持
        - **适用**: 日常文档、新闻资讯、商务文档
        - **获取**: [火山引擎控制台](https://console.volcengine.com/)

        #### 🤖 OpenAI
        - **优势**: 翻译质量高，上下文理解好
        - **适用**: 创意文档、文学作品、复杂文本
        - **获取**: [OpenAI平台](https://platform.openai.com/)

        ### 🌐 传统翻译服务

        #### 🔵 百度翻译
        - **优势**: 稳定可靠，中文支持好
        - **适用**: 通用文档翻译
        - **获取**: [百度翻译开放平台](https://fanyi-api.baidu.com/)

        #### 📚 有道翻译
        - **优势**: 词汇丰富，专业词典
        - **适用**: 学术文档、专业术语
        - **获取**: [有道智云](https://ai.youdao.com/)

        #### 🐧 腾讯翻译
        - **优势**: 企业级服务，安全性高
        - **适用**: 商务文档、敏感内容
        - **获取**: [腾讯云控制台](https://console.cloud.tencent.com/)

        ### ⚙️ 配置说明

        #### API密钥配置
        1. 在"设置"标签页选择对应的翻译服务
        2. 输入从官方平台获取的API密钥
        3. 点击"保存"按钮完成配置
        4. 系统会自动验证密钥有效性

        #### 翻译参数优化
        - **并发数**: 建议设置为3-5，过高可能触发API限制
        - **超时时间**: 网络较慢时可适当增加
        - **默认服务**: 选择最常用的翻译服务

        ### 🎯 最佳实践

        #### 选择翻译服务建议
        - **中英互译**: 推荐通义千问、百度翻译
        - **技术文档**: 推荐DeepSeek、OpenAI
        - **多语言**: 推荐豆包、有道翻译
        - **高质量要求**: 推荐OpenAI、通义千问

        #### 文档处理技巧
        1. **大文档**: 建议先测试小部分确认效果
        2. **复杂格式**: PDF格式通常解析效果最好
        3. **表格内容**: 建议使用保持布局选项
        4. **图片文字**: 暂不支持OCR，需要手动处理

        ### 🔧 故障排除

        #### 常见问题

        **Q: 翻译失败怎么办？**
        - 检查网络连接是否正常
        - 确认API密钥配置正确
        - 查看是否超出API调用限制
        - 尝试更换翻译服务提供商

        **Q: 翻译质量不理想？**
        - 尝试不同的翻译服务
        - 调整文本分段方式
        - 检查源语言检测是否正确
        - 考虑使用AI翻译服务

        **Q: 文档解析不完整？**
        - 确认文档格式在支持列表中
        - 尝试转换为PDF格式
        - 检查文档是否有密码保护
        - 确认文件大小在限制范围内

        **Q: 导出文件有问题？**
        - 检查导出格式设置
        - 确认有足够的磁盘空间
        - 尝试不同的导出模式
        - 检查文件权限设置

        ### 💡 使用技巧

        #### 提高翻译效率
        1. **批量处理**: 一次上传多个相似文档
        2. **模板复用**: 保存常用的翻译设置
        3. **预览确认**: 翻译前先预览文档结构
        4. **分段翻译**: 大文档可分段处理

        #### 质量控制
        1. **多服务对比**: 使用不同服务翻译同一内容
        2. **人工校对**: AI翻译后进行人工检查
        3. **术语统一**: 建立专业术语词典
        4. **格式检查**: 确保导出格式正确

        ### 📞 技术支持

        如果遇到问题，可以：
        1. 查看日志文件：`~/.document_translator/logs/`
        2. 检查配置文件：`~/.document_translator/config.yaml`
        3. 重启应用程序
        4. 联系技术支持

        ---

        **让AI翻译为您的工作提速！** 🚀
        """

        gr.Markdown(help_content)

    def _get_custom_css(self) -> str:
        """获取自定义CSS样式"""
        return """
        /* 全局重置和基础样式 */
        * {
            box-sizing: border-box;
        }

        /* 主容器样式 - 响应式设计 */
        .gradio-container {
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 0 !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
        }

        /* 内容包装器 */
        .gradio-container > div {
            width: 100% !important;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 标题区域响应式 */
        .gradio-container h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            font-weight: 600;
        }

        .gradio-container > div > div:first-child p {
            text-align: center;
            color: #6c757d;
            margin-bottom: 30px;
            font-size: clamp(0.9rem, 2vw, 1.1rem);
        }

        /* 标签页导航样式 - 响应式 */
        .tab-nav {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px 15px 0 0;
            border: 1px solid #dee2e6;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
            white-space: nowrap;
        }

        .tab-nav button {
            background: transparent;
            border: none;
            color: #495057;
            font-weight: 500;
            padding: 12px 16px;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: clamp(0.8rem, 2vw, 1rem);
            min-width: max-content;
        }

        .tab-nav button:hover {
            background: rgba(108, 117, 125, 0.1);
            transform: translateY(-2px);
            color: #2c3e50;
        }

        .tab-nav button.selected {
            background: #007bff;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        /* 内容区域样式 - 响应式 */
        .tabitem {
            background: white;
            backdrop-filter: blur(10px);
            border-radius: 0 0 15px 15px;
            padding: clamp(15px, 3vw, 30px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            width: 100%;
            border: 1px solid #dee2e6;
            border-top: none;
        }

        /* 行和列布局响应式 */
        .gradio-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            width: 100%;
        }

        .gradio-column {
            flex: 1;
            min-width: 300px;
        }

        /* 按钮样式 - 响应式 */
        .btn {
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 12px 24px;
            font-size: clamp(0.9rem, 2vw, 1rem);
            width: 100%;
            max-width: 200px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        /* 输入框样式 - 响应式 */
        .gr-textbox, .gr-dropdown {
            border-radius: 10px;
            border: 2px solid #e1e5e9;
            transition: all 0.3s ease;
            background: white;
            width: 100%;
            font-size: clamp(0.9rem, 2vw, 1rem);
        }

        .gr-textbox:focus, .gr-dropdown:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
        }

        /* 下拉框特殊样式 - 修复定位问题 */
        .gr-dropdown {
            position: relative !important;
            z-index: 100 !important;
        }

        .gr-dropdown .dropdown {
            position: relative !important;
            z-index: 101 !important;
        }

        .gr-dropdown .dropdown-menu {
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            right: 0 !important;
            background: white !important;
            border: 1px solid #dee2e6 !important;
            border-radius: 8px !important;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
            max-height: 250px !important;
            overflow-y: auto !important;
            z-index: 1050 !important;
            margin-top: 2px !important;
            min-width: 100% !important;
        }

        .gr-dropdown .dropdown-item {
            padding: 10px 15px !important;
            cursor: pointer !important;
            border-bottom: 1px solid #f8f9fa !important;
            transition: all 0.2s ease !important;
            font-size: 14px !important;
            color: #495057 !important;
        }

        .gr-dropdown .dropdown-item:hover {
            background-color: #e9ecef !important;
            color: #212529 !important;
        }

        .gr-dropdown .dropdown-item:last-child {
            border-bottom: none !important;
        }

        .gr-dropdown .dropdown-item.selected {
            background-color: #007bff !important;
            color: white !important;
        }

        /* 确保下拉框在所有容器之上 */
        .gradio-dropdown {
            position: relative !important;
            z-index: 100 !important;
        }

        /* 修复可能的容器层级问题 */
        .tabitem {
            position: relative !important;
            z-index: 1 !important;
        }

        /* 确保下拉框内容不被遮挡 */
        .gr-form {
            overflow: visible !important;
        }

        .gr-box {
            overflow: visible !important;
        }

        /* 手风琴样式 - 响应式 */
        .gr-accordion {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 15px;
            overflow: hidden;
            width: 100%;
        }

        .gr-accordion-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            padding: 15px 20px;
            font-weight: 600;
            color: #495057;
            font-size: clamp(0.9rem, 2vw, 1rem);
        }

        .gr-accordion-content {
            background: white;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }

        /* 进度条样式 */
        .progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            height: 8px;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
        }

        /* 文件上传区域样式 - 响应式 */
        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: clamp(20px, 4vw, 30px);
            text-align: center;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            transition: all 0.3s ease;
            width: 100%;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            transform: translateY(-2px);
        }

        /* 预览区域样式 - 响应式 */
        .preview-area {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
            max-height: 500px;
            overflow-y: auto;
            width: 100%;
        }

        /* 信息提示样式 */
        .gr-info {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: none;
            border-radius: 10px;
            color: #155724;
            padding: 12px 16px;
            box-shadow: 0 2px 10px rgba(21, 87, 36, 0.1);
        }

        .gr-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: none;
            border-radius: 10px;
            color: #856404;
            padding: 12px 16px;
            box-shadow: 0 2px 10px rgba(133, 100, 4, 0.1);
        }

        .gr-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: none;
            border-radius: 10px;
            color: #721c24;
            padding: 12px 16px;
            box-shadow: 0 2px 10px rgba(114, 28, 36, 0.1);
        }

        /* 卡片样式 - 响应式 */
        .info-card {
            background: white;
            border-radius: 12px;
            padding: clamp(15px, 3vw, 20px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #667eea;
            margin-bottom: 20px;
            width: 100%;
        }

        /* 统计信息样式 - 响应式网格 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin: 20px 0;
            width: 100%;
        }

        .stat-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .stat-number {
            font-size: clamp(1.2rem, 3vw, 1.5rem);
            font-weight: bold;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: clamp(0.8rem, 2vw, 0.9rem);
            color: #6c757d;
            margin-top: 5px;
        }

        /* 响应式设计 - 移动设备 */
        @media (max-width: 768px) {
            .gradio-container > div {
                padding: 10px;
            }

            .tabitem {
                padding: 15px;
            }

            .gradio-column {
                min-width: 100%;
                flex: none;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
                gap: 10px;
            }

            .btn {
                max-width: none;
                margin-bottom: 10px;
            }

            .tab-nav {
                border-radius: 10px 10px 0 0;
            }

            .tab-nav button {
                padding: 10px 12px;
                font-size: 0.9rem;
            }
        }

        /* 响应式设计 - 平板设备 */
        @media (max-width: 1024px) and (min-width: 769px) {
            .gradio-column {
                min-width: 45%;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            }
        }

        /* 响应式设计 - 大屏设备 */
        @media (min-width: 1400px) {
            .gradio-container > div {
                max-width: 1600px;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        /* 确保所有元素都能正确响应 */
        .gr-form, .gr-box {
            width: 100% !important;
        }

        /* 修复可能的布局问题 */
        .gradio-container .wrap {
            width: 100% !important;
        }
        """

    def _get_language_choices(self) -> Dict[str, str]:
        """获取语言选择"""
        return {
            "自动检测": "auto",
            "中文(简体)": "zh-CN",
            "中文(繁体)": "zh-TW",
            "英语": "en",
            "日语": "ja",
            "韩语": "ko",
            "法语": "fr",
            "德语": "de",
            "西班牙语": "es",
            "俄语": "ru",
            "阿拉伯语": "ar"
        }

    def _get_provider_choices(self) -> List[Tuple[str, str]]:
        """获取翻译服务提供商选择"""
        # 定义所有支持的翻译服务提供商及其显示名称
        all_provider_names = {
            "baidu": "🔵 百度翻译",
            "youdao": "📚 有道翻译",
            "tencent": "🐧 腾讯翻译",
            "qwen": "🔥 通义千问",
            "deepseek": "🧠 DeepSeek",
            "doubao": "🎯 豆包",
            "openai": "🤖 OpenAI",
            "google": "🌐 Google翻译"
        }

        # 优先级顺序：百度翻译优先
        priority_order = ["baidu", "qwen", "deepseek", "doubao", "openai", "youdao", "tencent", "google"]

        providers = []
        available_providers = self.translation_engine.get_available_providers()

        # 显示所有服务，区分已配置和未配置
        for provider in priority_order:
            name = all_provider_names.get(provider, provider.title())
            if provider in available_providers:
                # 已配置的服务
                providers.append((name, provider))
            elif provider == "google":
                # Google翻译可以免费使用
                providers.append((name, provider))
            else:
                # 未配置的服务，标注状态
                providers.append((f"{name} (未配置)", provider))

        # 添加其他可能的服务（如果有的话）
        for provider in available_providers:
            if provider not in priority_order:
                name = all_provider_names.get(provider, provider.title())
                providers.append((name, provider))

        return providers

    def _get_default_provider(self) -> str:
        """获取默认翻译服务提供商"""
        # 优先级顺序：百度翻译优先
        priority_order = ["baidu", "qwen", "deepseek", "doubao", "openai", "youdao", "tencent", "google"]

        available_providers = self.translation_engine.get_available_providers()

        # 按优先级返回第一个可用的服务
        for provider in priority_order:
            if provider in available_providers:
                return provider

        # 如果没有可用服务，返回百度翻译作为默认值
        return "baidu"

    async def _translate_text(self, text: str, source_lang: str, target_lang: str, provider: str) -> Tuple[str, str]:
        """翻译文本"""
        try:
            if not text.strip():
                return "", ""

            result = await self.translation_engine.translate_text(
                text, source_lang, target_lang, provider
            )

            if result.error:
                return f"翻译失败: {result.error}", f"提供商: {result.provider}"

            info = f"提供商: {result.provider} | 源语言: {result.source_language} | 置信度: {result.confidence:.2f}"
            return result.translated_text, info

        except Exception as e:
            self.logger.error(f"文本翻译失败: {e}")
            return f"翻译失败: {str(e)}", ""

    def _parse_document(self, file_path: str) -> Tuple[Dict, str]:
        """解析文档"""
        try:
            if not file_path:
                return {}, "<p>请先上传文档</p>"

            # 处理文档
            self.current_document = self.document_processor.process_document(file_path)

            # 生成预览HTML
            preview_html = self._generate_document_preview(self.current_document)

            # 返回文档信息
            doc_info = {
                "文件名": self.current_document['file_name'],
                "页数": self.current_document['stats']['total_pages'],
                "文本元素": self.current_document['stats']['text_elements'],
                "图像元素": self.current_document['stats']['image_elements'],
                "表格元素": self.current_document['stats']['table_elements'],
                "总字符数": self.current_document['stats']['total_characters'],
                "估计单词数": self.current_document['stats']['estimated_words']
            }

            return doc_info, preview_html

        except Exception as e:
            self.logger.error(f"文档解析失败: {e}")
            return {"错误": str(e)}, f"<p>文档解析失败: {str(e)}</p>"

    async def _translate_document(self, source_lang: str, target_lang: str, provider: str) -> str:
        """翻译文档"""
        try:
            if not self.current_document:
                return "<p>请先解析文档</p>"

            # 提取所有文本内容
            all_texts = []
            text_mapping = []  # 记录文本在页面中的位置

            for page_idx, page in enumerate(self.current_document['pages']):
                for elem_idx, element in enumerate(page.elements):
                    if element.element_type == "text" and element.content.strip():
                        all_texts.append(element.content)
                        text_mapping.append((page_idx, elem_idx))

            if not all_texts:
                return "<p>文档中没有找到可翻译的文本</p>"

            # 批量翻译
            translation_results = await self.translation_engine.translate_batch(
                all_texts, source_lang, target_lang, provider,
                max_concurrent=self.config.translation.max_concurrent
            )

            # 创建翻译后的文档页面
            translated_pages = []
            result_idx = 0

            for page in self.current_document['pages']:
                translated_elements = []

                for element in page.elements:
                    if element.element_type == "text" and element.content.strip():
                        if result_idx < len(translation_results):
                            result = translation_results[result_idx]
                            if not result.error:
                                # 创建翻译后的元素
                                translated_element = DocumentElement(
                                    element_type=element.element_type,
                                    content=result.translated_text,
                                    position=element.position,
                                    page_number=element.page_number,
                                    font_info=element.font_info,
                                    style_info=element.style_info
                                )
                                translated_elements.append(translated_element)
                            else:
                                # 翻译失败，保留原文
                                translated_elements.append(element)
                            result_idx += 1
                    else:
                        # 非文本元素保持不变
                        translated_elements.append(element)

                translated_page = DocumentPage(
                    page_number=page.page_number,
                    elements=translated_elements,
                    width=page.width,
                    height=page.height
                )
                translated_pages.append(translated_page)

            # 保存翻译结果
            self.translated_document = {
                'pages': translated_pages,
                'stats': self.current_document['stats'],
                'file_name': self.current_document['file_name'],
                'translation_info': {
                    'source_lang': source_lang,
                    'target_lang': target_lang,
                    'provider': provider,
                    'total_texts': len(all_texts),
                    'successful_translations': sum(1 for r in translation_results if not r.error)
                }
            }

            # 生成翻译预览
            preview_html = self._generate_translation_preview(self.translated_document)

            return preview_html

        except Exception as e:
            self.logger.error(f"文档翻译失败: {e}")
            return f"<p>文档翻译失败: {str(e)}</p>"

    def _export_document(self, export_format: str, export_mode: str) -> Optional[str]:
        """导出文档"""
        try:
            if not self.current_document:
                gr.Warning("请先解析文档")
                return None

            if export_mode in ["translated_only", "bilingual"] and not self.translated_document:
                gr.Warning("请先翻译文档")
                return None

            # 创建临时文件
            temp_dir = self.config.get_temp_dir()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"translated_document_{timestamp}{export_format}"
            output_path = temp_dir / filename

            # 确定要导出的页面
            if export_mode == "original_only":
                pages_to_export = self.current_document['pages']
            elif export_mode == "translated_only":
                pages_to_export = self.translated_document['pages']
            else:  # bilingual
                pages_to_export = None  # 将在导出器中处理

            # 导出文档
            if export_mode == "bilingual":
                success = self.document_exporter.export_translated_document(
                    self.current_document['pages'],
                    self.translated_document['pages'],
                    str(output_path),
                    export_mode,
                    title=f"翻译文档 - {self.current_document['file_name']}"
                )
            else:
                success = self.document_exporter.exporter_factory.export_document(
                    pages_to_export,
                    str(output_path),
                    title=f"翻译文档 - {self.current_document['file_name']}"
                )

            if success:
                gr.Info("文档导出成功")
                return str(output_path)
            else:
                gr.Error("文档导出失败")
                return None

        except Exception as e:
            self.logger.error(f"文档导出失败: {e}")
            gr.Error(f"文档导出失败: {str(e)}")
            return None

    def _generate_document_preview(self, document: Dict) -> str:
        """生成文档预览HTML"""
        html_content = ["<div style='font-family: Arial, sans-serif; line-height: 1.6;'>"]

        for page in document['pages']:
            html_content.append(f"<h3>第 {page.page_number} 页</h3>")
            html_content.append("<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>")

            for element in page.elements:
                if element.element_type == "text":
                    html_content.append(f"<p>{element.content}</p>")
                elif element.element_type == "table":
                    html_content.append("<div style='background: #f5f5f5; padding: 5px; margin: 5px 0;'>")
                    html_content.append("<strong>表格内容:</strong><br>")
                    html_content.append(f"<pre>{element.content}</pre>")
                    html_content.append("</div>")
                elif element.element_type == "image":
                    html_content.append("<div style='background: #e8f4fd; padding: 5px; margin: 5px 0;'>")
                    html_content.append(f"<strong>图像:</strong> {element.content}")
                    html_content.append("</div>")

            html_content.append("</div>")

        html_content.append("</div>")
        return "".join(html_content)

    def _generate_translation_preview(self, translated_document: Dict) -> str:
        """生成翻译预览HTML"""
        html_content = ["<div style='font-family: Arial, sans-serif; line-height: 1.6;'>"]

        # 翻译信息
        info = translated_document['translation_info']
        html_content.append("<div style='background: #e8f5e8; padding: 10px; margin-bottom: 20px; border-radius: 5px;'>")
        html_content.append(f"<strong>翻译信息:</strong><br>")
        html_content.append(f"源语言: {info['source_lang']} → 目标语言: {info['target_lang']}<br>")
        html_content.append(f"翻译服务: {info['provider']}<br>")
        html_content.append(f"成功翻译: {info['successful_translations']}/{info['total_texts']} 个文本段落")
        html_content.append("</div>")

        # 翻译内容
        for page in translated_document['pages']:
            html_content.append(f"<h3>第 {page.page_number} 页</h3>")
            html_content.append("<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>")

            for element in page.elements:
                if element.element_type == "text":
                    html_content.append(f"<p>{element.content}</p>")
                elif element.element_type == "table":
                    html_content.append("<div style='background: #f5f5f5; padding: 5px; margin: 5px 0;'>")
                    html_content.append("<strong>表格内容:</strong><br>")
                    html_content.append(f"<pre>{element.content}</pre>")
                    html_content.append("</div>")
                elif element.element_type == "image":
                    html_content.append("<div style='background: #e8f4fd; padding: 5px; margin: 5px 0;'>")
                    html_content.append(f"<strong>图像:</strong> {element.content}")
                    html_content.append("</div>")

            html_content.append("</div>")

        html_content.append("</div>")
        return "".join(html_content)

    def _save_baidu_settings(self, app_id: str, secret: str):
        """保存百度翻译设置"""
        if app_id.strip() and secret.strip():
            self.config.set_api_key("baidu_app_id", app_id.strip())
            self.config.set_api_key("baidu_secret", secret.strip())
            gr.Info("百度翻译设置已保存")
            self.translation_engine = TranslationEngine(self.config)
        else:
            gr.Warning("百度翻译APP ID和密钥都不能为空")

    def _save_youdao_settings(self, key: str, secret: str):
        """保存有道翻译设置"""
        if key.strip() and secret.strip():
            self.config.set_api_key("youdao_key", key.strip())
            self.config.set_api_key("youdao_secret", secret.strip())
            gr.Info("有道翻译设置已保存")
            self.translation_engine = TranslationEngine(self.config)
        else:
            gr.Warning("有道翻译应用ID和密钥都不能为空")

    def _save_tencent_settings(self, id: str, key: str):
        """保存腾讯翻译设置"""
        if id.strip() and key.strip():
            self.config.set_api_key("tencent_id", id.strip())
            self.config.set_api_key("tencent_key", key.strip())
            gr.Info("腾讯翻译设置已保存")
            self.translation_engine = TranslationEngine(self.config)
        else:
            gr.Warning("腾讯云SecretId和SecretKey都不能为空")

    def _save_api_key(self, provider: str, key: str):
        """保存API密钥"""
        if key.strip():
            self.config.set_api_key(provider, key.strip())
            gr.Info(f"{provider.title()} API密钥已保存")
            # 重新初始化翻译引擎
            self.translation_engine = TranslationEngine(self.config)
        else:
            gr.Warning("API密钥不能为空")

    def _save_azure_settings(self, key: str, endpoint: str):
        """保存Azure设置"""
        if key.strip() and endpoint.strip():
            self.config.set_api_key("azure", key.strip())
            self.config.set_api_key("azure_endpoint", endpoint.strip())
            gr.Info("Azure设置已保存")
            # 重新初始化翻译引擎
            self.translation_engine = TranslationEngine(self.config)
        else:
            gr.Warning("Azure API密钥和端点都不能为空")

    def _save_general_settings(self, default_provider: str, max_concurrent: int,
                             timeout: int, max_file_size: int, preserve_layout: bool):
        """保存通用设置"""
        try:
            self.config.translation.default_provider = default_provider
            self.config.translation.max_concurrent = max_concurrent
            self.config.translation.timeout = timeout
            self.config.document.max_file_size = max_file_size
            self.config.document.preserve_layout = preserve_layout

            self.config.save_config()
            gr.Info("设置已保存")

        except Exception as e:
            gr.Error(f"保存设置失败: {str(e)}")

    def run(self, **kwargs):
        """启动应用"""
        default_kwargs = {
            "server_name": "0.0.0.0",
            "server_port": 7860,
            "share": False,
            "debug": False
        }
        default_kwargs.update(kwargs)

        self.logger.info("启动Gradio界面...")
        self.interface.launch(**default_kwargs)

# 导入必要的模块
from datetime import datetime
from ..document.parser import DocumentElement, DocumentPage