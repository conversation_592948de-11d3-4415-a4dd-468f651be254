#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
管理应用程序的配置信息，包括API密钥、翻译设置等
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from cryptography.fernet import Fernet
import base64

logger = logging.getLogger(__name__)

@dataclass
class TranslationConfig:
    """翻译配置"""
    default_provider: str = "baidu"  # 默认使用百度翻译
    max_concurrent: int = 3
    timeout: int = 30
    retry_count: int = 2
    chunk_size: int = 1000

@dataclass
class DocumentConfig:
    """文档配置"""
    max_file_size: int = 100  # MB
    supported_formats: list = None
    preserve_layout: bool = True
    ocr_enabled: bool = False
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['.pdf', '.docx', '.txt', '.md']

@dataclass
class UIConfig:
    """界面配置"""
    theme: str = "soft"
    language: str = "zh-CN"
    auto_save: bool = True
    show_advanced: bool = False

class Config:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """初始化配置管理器"""
        
        # 确定配置目录
        if config_dir is None:
            self.config_dir = Path.home() / ".document_translator"
        else:
            self.config_dir = Path(config_dir)
        
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置文件路径
        self.config_file = self.config_dir / "config.yaml"
        self.api_keys_file = self.config_dir / "api_keys.enc"
        self.key_file = self.config_dir / ".key"
        
        # 初始化配置
        self.translation = TranslationConfig()
        self.document = DocumentConfig()
        self.ui = UIConfig()
        
        # API密钥存储
        self._api_keys: Dict[str, str] = {}
        self._cipher_suite = None
        
        # 加载配置
        self._init_encryption()
        self.load()
        
        logger.info("配置管理器初始化完成")
    
    def _init_encryption(self):
        """初始化加密"""
        try:
            if self.key_file.exists():
                # 加载现有密钥
                with open(self.key_file, 'rb') as f:
                    key = f.read()
            else:
                # 生成新密钥
                key = Fernet.generate_key()
                with open(self.key_file, 'wb') as f:
                    f.write(key)
                # 设置文件权限（仅所有者可读写）
                os.chmod(self.key_file, 0o600)
            
            self._cipher_suite = Fernet(key)
            
        except Exception as e:
            logger.error(f"加密初始化失败: {e}")
            # 如果加密失败，使用明文存储（不推荐用于生产环境）
            self._cipher_suite = None
    
    def load(self):
        """加载配置"""
        try:
            # 加载主配置文件
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f) or {}
                
                # 更新配置对象
                if 'translation' in config_data:
                    for key, value in config_data['translation'].items():
                        if hasattr(self.translation, key):
                            setattr(self.translation, key, value)
                
                if 'document' in config_data:
                    for key, value in config_data['document'].items():
                        if hasattr(self.document, key):
                            setattr(self.document, key, value)
                
                if 'ui' in config_data:
                    for key, value in config_data['ui'].items():
                        if hasattr(self.ui, key):
                            setattr(self.ui, key, value)
            
            # 加载API密钥
            self._load_api_keys()
            
            logger.info("配置加载完成")
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            # 使用默认配置
            self._create_default_config()
    
    def save(self):
        """保存配置"""
        try:
            # 准备配置数据
            config_data = {
                'translation': asdict(self.translation),
                'document': asdict(self.document),
                'ui': asdict(self.ui)
            }
            
            # 保存主配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            # 保存API密钥
            self._save_api_keys()
            
            logger.info("配置保存完成")
            
        except Exception as e:
            logger.error(f"配置保存失败: {e}")
            raise
    
    def _load_api_keys(self):
        """加载API密钥"""
        try:
            if not self.api_keys_file.exists():
                return
            
            with open(self.api_keys_file, 'rb') as f:
                encrypted_data = f.read()
            
            if self._cipher_suite and encrypted_data:
                # 解密数据
                decrypted_data = self._cipher_suite.decrypt(encrypted_data)
                api_keys_data = yaml.safe_load(decrypted_data.decode('utf-8'))
                self._api_keys = api_keys_data or {}
            else:
                # 如果没有加密套件，尝试直接读取（向后兼容）
                with open(self.api_keys_file, 'r', encoding='utf-8') as f:
                    self._api_keys = yaml.safe_load(f) or {}
            
        except Exception as e:
            logger.error(f"API密钥加载失败: {e}")
            self._api_keys = {}
    
    def _save_api_keys(self):
        """保存API密钥"""
        try:
            if not self._api_keys:
                return
            
            # 序列化数据
            api_keys_yaml = yaml.dump(self._api_keys, default_flow_style=False)
            
            if self._cipher_suite:
                # 加密保存
                encrypted_data = self._cipher_suite.encrypt(api_keys_yaml.encode('utf-8'))
                with open(self.api_keys_file, 'wb') as f:
                    f.write(encrypted_data)
            else:
                # 明文保存（不推荐）
                with open(self.api_keys_file, 'w', encoding='utf-8') as f:
                    f.write(api_keys_yaml)
            
            # 设置文件权限
            os.chmod(self.api_keys_file, 0o600)
            
        except Exception as e:
            logger.error(f"API密钥保存失败: {e}")
            raise
    
    def _create_default_config(self):
        """创建默认配置"""
        self.translation = TranslationConfig()
        self.document = DocumentConfig()
        self.ui = UIConfig()
        self._api_keys = {}
        
        # 保存默认配置
        self.save()
        logger.info("已创建默认配置")
    
    def get_api_key(self, service: str) -> Optional[str]:
        """获取API密钥"""
        return self._api_keys.get(service)
    
    def set_api_key(self, service: str, api_key: str):
        """设置API密钥"""
        if api_key.strip():
            self._api_keys[service] = api_key.strip()
            # 立即保存
            self._save_api_keys()
            logger.info(f"已设置 {service} API密钥")
        else:
            # 删除空密钥
            if service in self._api_keys:
                del self._api_keys[service]
                self._save_api_keys()
                logger.info(f"已删除 {service} API密钥")
    
    def remove_api_key(self, service: str):
        """删除API密钥"""
        if service in self._api_keys:
            del self._api_keys[service]
            self._save_api_keys()
            logger.info(f"已删除 {service} API密钥")
    
    def get_available_providers(self) -> list:
        """获取可用的翻译服务提供商"""
        providers = []
        
        # 检查各个服务的API密钥
        if self.get_api_key('baidu_app_id') and self.get_api_key('baidu_secret'):
            providers.append('baidu')
        
        if self.get_api_key('youdao_key') and self.get_api_key('youdao_secret'):
            providers.append('youdao')
        
        if self.get_api_key('tencent_id') and self.get_api_key('tencent_key'):
            providers.append('tencent')
        
        if self.get_api_key('qwen'):
            providers.append('qwen')
        
        if self.get_api_key('deepseek'):
            providers.append('deepseek')
        
        if self.get_api_key('doubao'):
            providers.append('doubao')
        
        if self.get_api_key('openai'):
            providers.append('openai')
        
        # 如果没有配置任何服务，返回google作为默认选项
        if not providers:
            providers.append('google')
        
        return providers
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        issues = []
        
        # 检查翻译配置
        if self.translation.max_concurrent < 1:
            issues.append("最大并发数必须大于0")
        
        if self.translation.timeout < 5:
            issues.append("超时时间不能少于5秒")
        
        # 检查文档配置
        if self.document.max_file_size < 1:
            issues.append("最大文件大小必须大于0")
        
        # 检查API密钥
        available_providers = self.get_available_providers()
        if not available_providers or available_providers == ['google']:
            issues.append("建议配置至少一个翻译服务的API密钥")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'available_providers': available_providers
        }
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self._create_default_config()
        logger.info("配置已重置为默认值")
    
    def export_config(self, export_path: str, include_api_keys: bool = False):
        """导出配置"""
        try:
            config_data = {
                'translation': asdict(self.translation),
                'document': asdict(self.document),
                'ui': asdict(self.ui)
            }
            
            if include_api_keys:
                config_data['api_keys'] = self._api_keys
            
            with open(export_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置已导出到: {export_path}")
            
        except Exception as e:
            logger.error(f"配置导出失败: {e}")
            raise
    
    def import_config(self, import_path: str):
        """导入配置"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 更新配置
            if 'translation' in config_data:
                for key, value in config_data['translation'].items():
                    if hasattr(self.translation, key):
                        setattr(self.translation, key, value)
            
            if 'document' in config_data:
                for key, value in config_data['document'].items():
                    if hasattr(self.document, key):
                        setattr(self.document, key, value)
            
            if 'ui' in config_data:
                for key, value in config_data['ui'].items():
                    if hasattr(self.ui, key):
                        setattr(self.ui, key, value)
            
            if 'api_keys' in config_data:
                self._api_keys.update(config_data['api_keys'])
            
            # 保存配置
            self.save()
            
            logger.info(f"配置已从 {import_path} 导入")
            
        except Exception as e:
            logger.error(f"配置导入失败: {e}")
            raise
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Config(providers={self.get_available_providers()}, default={self.translation.default_provider})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()
