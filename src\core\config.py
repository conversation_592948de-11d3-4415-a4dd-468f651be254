#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
管理应用程序的所有配置信息
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

@dataclass
class TranslationConfig:
    """翻译配置"""
    default_provider: str = "google"
    source_language: str = "auto"
    target_language: str = "zh-CN"
    max_concurrent: int = 5
    timeout: int = 30
    retry_count: int = 3

@dataclass
class UIConfig:
    """界面配置"""
    theme: str = "light"
    language: str = "zh-CN"
    window_width: int = 1200
    window_height: int = 800
    font_size: int = 12

@dataclass
class DocumentConfig:
    """文档处理配置"""
    supported_formats: list = None
    max_file_size: int = 100  # MB
    output_format: str = "pdf"
    preserve_layout: bool = True
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = [".pdf", ".docx", ".txt", ".md", ".pptx", ".xlsx"]

class Config:
    """主配置类"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_dir = Path.home() / ".document_translator"
        self.config_dir.mkdir(exist_ok=True)
        
        self.config_file = self.config_dir / "config.yaml"
        
        # 初始化默认配置
        self.translation = TranslationConfig()
        self.ui = UIConfig()
        self.document = DocumentConfig()
        self.api_keys = {}
        
        # 加载配置
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                
                if data:
                    # 更新配置
                    if 'translation' in data:
                        self.translation = TranslationConfig(**data['translation'])
                    if 'ui' in data:
                        self.ui = UIConfig(**data['ui'])
                    if 'document' in data:
                        self.document = DocumentConfig(**data['document'])
                    if 'api_keys' in data:
                        self.api_keys = data['api_keys']
                        
            except Exception as e:
                print(f"加载配置文件失败: {e}")
    
    def save_config(self):
        """保存配置文件"""
        try:
            config_data = {
                'translation': asdict(self.translation),
                'ui': asdict(self.ui),
                'document': asdict(self.document),
                'api_keys': self.api_keys
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
                
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """获取API密钥"""
        return self.api_keys.get(provider)
    
    def set_api_key(self, provider: str, key: str):
        """设置API密钥"""
        self.api_keys[provider] = key
        self.save_config()
    
    def get_cache_dir(self) -> Path:
        """获取缓存目录"""
        cache_dir = self.config_dir / "cache"
        cache_dir.mkdir(exist_ok=True)
        return cache_dir
    
    def get_temp_dir(self) -> Path:
        """获取临时目录"""
        temp_dir = self.config_dir / "temp"
        temp_dir.mkdir(exist_ok=True)
        return temp_dir
