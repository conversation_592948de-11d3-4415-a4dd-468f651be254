#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档解析模块
支持多种文档格式的解析和内容提取
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from abc import ABC, abstractmethod
from dataclasses import dataclass

# 文档处理库
import fitz  # PyMuPDF
from docx import Document as DocxDocument

@dataclass
class DocumentElement:
    """文档元素"""
    element_type: str  # text, image, table, formula
    content: str
    position: Tuple[float, float, float, float]  # x1, y1, x2, y2
    page_number: int
    font_info: Optional[Dict] = None
    style_info: Optional[Dict] = None

@dataclass
class DocumentPage:
    """文档页面"""
    page_number: int
    elements: List[DocumentElement]
    width: float
    height: float

class BaseDocumentParser(ABC):
    """文档解析器基类"""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    @abstractmethod
    def parse(self, file_path: str) -> List[DocumentPage]:
        """解析文档"""
        pass

    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式"""
        pass

class PDFParser(BaseDocumentParser):
    """PDF文档解析器"""

    def __init__(self, use_layout_analysis: bool = True):
        super().__init__()
        self.use_layout_analysis = use_layout_analysis

    def parse(self, file_path: str) -> List[DocumentPage]:
        """解析PDF文档"""
        try:
            doc = fitz.open(file_path)
            pages = []

            for page_num in range(len(doc)):
                page = doc[page_num]
                page_width = page.rect.width
                page_height = page.rect.height

                elements = []

                # 提取文本块
                text_blocks = page.get_text("dict")
                for block in text_blocks["blocks"]:
                    if "lines" in block:
                        for line in block["lines"]:
                            for span in line["spans"]:
                                if span["text"].strip():
                                    bbox = span["bbox"]
                                    element = DocumentElement(
                                        element_type="text",
                                        content=span["text"],
                                        position=(bbox[0], bbox[1], bbox[2], bbox[3]),
                                        page_number=page_num + 1,
                                        font_info={
                                            "font": span["font"],
                                            "size": span["size"],
                                            "flags": span["flags"]
                                        }
                                    )
                                    elements.append(element)

                # 提取图像 - 使用更安全的方法
                try:
                    image_list = page.get_images()
                    for img_index, img in enumerate(image_list):
                        try:
                            # 使用更简单的方法来避免版本兼容性问题
                            # 只记录图像存在，不获取精确位置
                            element = DocumentElement(
                                element_type="image",
                                content=f"image_{page_num}_{img_index}",
                                position=(0, img_index * 50, 100, (img_index + 1) * 50),  # 使用简单的位置计算
                                page_number=page_num + 1
                            )
                            elements.append(element)
                        except Exception as img_error:
                            self.logger.warning(f"处理图像 {img_index} 时出错: {img_error}")
                            continue
                except Exception as e:
                    self.logger.warning(f"提取图像时出错: {e}")
                    # 继续处理，不中断整个解析过程

                # 提取表格（简化版本）
                try:
                    tables = page.find_tables()
                    for table_index, table in enumerate(tables):
                        try:
                            bbox = table.bbox
                            table_content = self._extract_table_content(table)
                            element = DocumentElement(
                                element_type="table",
                                content=table_content,
                                position=(bbox[0], bbox[1], bbox[2], bbox[3]),
                                page_number=page_num + 1
                            )
                            elements.append(element)
                        except Exception as table_error:
                            self.logger.warning(f"处理表格 {table_index} 时出错: {table_error}")
                            # 创建一个默认的表格元素
                            element = DocumentElement(
                                element_type="table",
                                content="表格内容解析失败",
                                position=(0, 0, 100, 100),
                                page_number=page_num + 1
                            )
                            elements.append(element)
                except Exception as e:
                    self.logger.warning(f"查找表格时出错: {e}")
                    # 继续处理，不中断整个解析过程

                page_obj = DocumentPage(
                    page_number=page_num + 1,
                    elements=elements,
                    width=page_width,
                    height=page_height
                )
                pages.append(page_obj)

            doc.close()
            return pages

        except Exception as e:
            self.logger.error(f"PDF解析失败: {e}")
            return []

    def _extract_table_content(self, table) -> str:
        """提取表格内容"""
        try:
            table_data = table.extract()
            content = ""
            for row in table_data:
                content += "\t".join([cell or "" for cell in row]) + "\n"
            return content.strip()
        except:
            return "表格内容"

    def get_supported_formats(self) -> List[str]:
        return [".pdf"]

class DocxParser(BaseDocumentParser):
    """DOCX文档解析器"""

    def parse(self, file_path: str) -> List[DocumentPage]:
        """解析DOCX文档"""
        try:
            doc = DocxDocument(file_path)
            elements = []

            for para_index, paragraph in enumerate(doc.paragraphs):
                if paragraph.text.strip():
                    element = DocumentElement(
                        element_type="text",
                        content=paragraph.text,
                        position=(0, para_index * 20, 500, (para_index + 1) * 20),
                        page_number=1,  # DOCX通常作为单页处理
                        style_info={
                            "style": paragraph.style.name if paragraph.style else None
                        }
                    )
                    elements.append(element)

            # 处理表格
            for table_index, table in enumerate(doc.tables):
                table_content = self._extract_docx_table(table)
                element = DocumentElement(
                    element_type="table",
                    content=table_content,
                    position=(0, len(elements) * 20, 500, (len(elements) + 5) * 20),
                    page_number=1
                )
                elements.append(element)

            page = DocumentPage(
                page_number=1,
                elements=elements,
                width=595,  # A4宽度
                height=842   # A4高度
            )

            return [page]

        except Exception as e:
            self.logger.error(f"DOCX解析失败: {e}")
            return []

    def _extract_docx_table(self, table) -> str:
        """提取DOCX表格内容"""
        content = ""
        for row in table.rows:
            row_content = []
            for cell in row.cells:
                row_content.append(cell.text.strip())
            content += "\t".join(row_content) + "\n"
        return content.strip()

    def get_supported_formats(self) -> List[str]:
        return [".docx"]

class TxtParser(BaseDocumentParser):
    """文本文档解析器"""

    def parse(self, file_path: str) -> List[DocumentPage]:
        """解析文本文档"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 按行分割文本
            lines = content.split('\n')
            elements = []

            for line_index, line in enumerate(lines):
                if line.strip():
                    element = DocumentElement(
                        element_type="text",
                        content=line,
                        position=(0, line_index * 15, 500, (line_index + 1) * 15),
                        page_number=1
                    )
                    elements.append(element)

            page = DocumentPage(
                page_number=1,
                elements=elements,
                width=595,
                height=842
            )

            return [page]

        except Exception as e:
            self.logger.error(f"文本解析失败: {e}")
            return []

    def get_supported_formats(self) -> List[str]:
        return [".txt", ".md"]

class DocumentParserFactory:
    """文档解析器工厂"""

    def __init__(self):
        self.parsers = {
            '.pdf': PDFParser(),
            '.docx': DocxParser(),
            '.txt': TxtParser(),
            '.md': TxtParser(),
        }

    def get_parser(self, file_path: str) -> Optional[BaseDocumentParser]:
        """根据文件扩展名获取解析器"""
        file_ext = Path(file_path).suffix.lower()
        return self.parsers.get(file_ext)

    def get_supported_formats(self) -> List[str]:
        """获取所有支持的格式"""
        formats = []
        for parser in self.parsers.values():
            formats.extend(parser.get_supported_formats())
        return list(set(formats))

    def parse_document(self, file_path: str) -> List[DocumentPage]:
        """解析文档"""
        parser = self.get_parser(file_path)
        if parser:
            return parser.parse(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {Path(file_path).suffix}")

class DocumentProcessor:
    """文档处理器"""

    def __init__(self, config):
        self.config = config
        self.parser_factory = DocumentParserFactory()
        self.logger = logging.getLogger(__name__)

    def process_document(self, file_path: str) -> Dict[str, Any]:
        """处理文档"""
        try:
            # 检查文件大小
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            if file_size > self.config.document.max_file_size:
                raise ValueError(f"文件大小超过限制: {file_size:.1f}MB > {self.config.document.max_file_size}MB")

            # 解析文档
            pages = self.parser_factory.parse_document(file_path)

            # 提取文本内容
            text_content = self._extract_text_content(pages)

            # 统计信息
            stats = self._calculate_stats(pages)

            return {
                'pages': pages,
                'text_content': text_content,
                'stats': stats,
                'file_path': file_path,
                'file_name': Path(file_path).name
            }

        except Exception as e:
            self.logger.error(f"文档处理失败: {e}")
            raise

    def _extract_text_content(self, pages: List[DocumentPage]) -> List[str]:
        """提取文本内容"""
        text_content = []
        for page in pages:
            page_text = []
            for element in page.elements:
                if element.element_type == "text":
                    page_text.append(element.content)
            text_content.append('\n'.join(page_text))
        return text_content

    def _calculate_stats(self, pages: List[DocumentPage]) -> Dict[str, Any]:
        """计算统计信息"""
        total_elements = 0
        text_elements = 0
        image_elements = 0
        table_elements = 0
        total_chars = 0

        for page in pages:
            total_elements += len(page.elements)
            for element in page.elements:
                if element.element_type == "text":
                    text_elements += 1
                    total_chars += len(element.content)
                elif element.element_type == "image":
                    image_elements += 1
                elif element.element_type == "table":
                    table_elements += 1

        return {
            'total_pages': len(pages),
            'total_elements': total_elements,
            'text_elements': text_elements,
            'image_elements': image_elements,
            'table_elements': table_elements,
            'total_characters': total_chars,
            'estimated_words': total_chars // 5  # 粗略估算单词数
        }
