#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档导出模块
支持多种格式的翻译结果导出
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod
from datetime import datetime

# 文档生成库
import fitz  # PyMuPDF
from docx import Document as DocxDocument
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import markdown
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from .parser import DocumentPage, DocumentElement

class BaseExporter(ABC):
    """导出器基类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def export(self, pages: List[DocumentPage], output_path: str, **kwargs) -> bool:
        """导出文档"""
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """获取支持的导出格式"""
        pass

class PDFExporter(BaseExporter):
    """PDF导出器"""
    
    def __init__(self):
        super().__init__()
        self._register_fonts()
    
    def _register_fonts(self):
        """注册中文字体"""
        try:
            # 尝试注册系统中文字体
            font_paths = [
                "/System/Library/Fonts/PingFang.ttc",  # macOS
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
                "C:/Windows/Fonts/msyh.ttc",  # Windows
                "C:/Windows/Fonts/simsun.ttc",  # Windows
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                        break
                    except:
                        continue
        except Exception as e:
            self.logger.warning(f"字体注册失败: {e}")
    
    def export(self, pages: List[DocumentPage], output_path: str, **kwargs) -> bool:
        """导出为PDF"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            styles = getSampleStyleSheet()
            
            # 创建中文样式
            chinese_style = ParagraphStyle(
                'ChineseStyle',
                parent=styles['Normal'],
                fontName='ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
                fontSize=12,
                leading=18,
                spaceAfter=6
            )
            
            story = []
            
            # 添加标题
            title = kwargs.get('title', '翻译文档')
            title_style = ParagraphStyle(
                'TitleStyle',
                parent=chinese_style,
                fontSize=16,
                alignment=WD_ALIGN_PARAGRAPH.CENTER,
                spaceAfter=12
            )
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 12))
            
            # 添加页面内容
            for page in pages:
                # 页面标题
                page_title = f"第 {page.page_number} 页"
                story.append(Paragraph(page_title, chinese_style))
                story.append(Spacer(1, 6))
                
                # 添加元素
                for element in page.elements:
                    if element.element_type == "text":
                        story.append(Paragraph(element.content, chinese_style))
                        story.append(Spacer(1, 3))
                    elif element.element_type == "table":
                        # 处理表格
                        table_data = self._parse_table_content(element.content)
                        if table_data:
                            table = Table(table_data)
                            table.setStyle(TableStyle([
                                ('BACKGROUND', (0, 0), (-1, 0), '#f0f0f0'),
                                ('TEXTCOLOR', (0, 0), (-1, 0), '#000000'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                                ('FONTNAME', (0, 0), (-1, -1), 'ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
                                ('FONTSIZE', (0, 0), (-1, -1), 10),
                                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                                ('BACKGROUND', (0, 1), (-1, -1), '#ffffff'),
                                ('GRID', (0, 0), (-1, -1), 1, '#000000')
                            ]))
                            story.append(table)
                            story.append(Spacer(1, 6))
                
                story.append(Spacer(1, 12))
            
            # 生成PDF
            doc.build(story)
            return True
            
        except Exception as e:
            self.logger.error(f"PDF导出失败: {e}")
            return False
    
    def _parse_table_content(self, content: str) -> List[List[str]]:
        """解析表格内容"""
        try:
            lines = content.strip().split('\n')
            table_data = []
            for line in lines:
                row = line.split('\t')
                table_data.append(row)
            return table_data
        except:
            return []
    
    def get_supported_formats(self) -> List[str]:
        return [".pdf"]

class DocxExporter(BaseExporter):
    """DOCX导出器"""
    
    def export(self, pages: List[DocumentPage], output_path: str, **kwargs) -> bool:
        """导出为DOCX"""
        try:
            doc = DocxDocument()
            
            # 设置文档标题
            title = kwargs.get('title', '翻译文档')
            title_para = doc.add_heading(title, 0)
            title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加页面内容
            for page in pages:
                # 页面标题
                doc.add_heading(f'第 {page.page_number} 页', level=1)
                
                # 添加元素
                for element in page.elements:
                    if element.element_type == "text":
                        para = doc.add_paragraph(element.content)
                        # 设置字体
                        for run in para.runs:
                            run.font.size = Pt(12)
                    
                    elif element.element_type == "table":
                        # 处理表格
                        table_data = self._parse_table_content(element.content)
                        if table_data and len(table_data) > 0:
                            table = doc.add_table(rows=len(table_data), cols=len(table_data[0]))
                            table.style = 'Table Grid'
                            
                            for i, row_data in enumerate(table_data):
                                for j, cell_data in enumerate(row_data):
                                    table.cell(i, j).text = cell_data
                
                # 添加分页符（除了最后一页）
                if page.page_number < len(pages):
                    doc.add_page_break()
            
            # 保存文档
            doc.save(output_path)
            return True
            
        except Exception as e:
            self.logger.error(f"DOCX导出失败: {e}")
            return False
    
    def _parse_table_content(self, content: str) -> List[List[str]]:
        """解析表格内容"""
        try:
            lines = content.strip().split('\n')
            table_data = []
            for line in lines:
                row = line.split('\t')
                table_data.append(row)
            return table_data
        except:
            return []
    
    def get_supported_formats(self) -> List[str]:
        return [".docx"]

class TxtExporter(BaseExporter):
    """文本导出器"""
    
    def export(self, pages: List[DocumentPage], output_path: str, **kwargs) -> bool:
        """导出为文本"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                # 写入标题
                title = kwargs.get('title', '翻译文档')
                f.write(f"{title}\n")
                f.write("=" * len(title) + "\n\n")
                
                # 写入页面内容
                for page in pages:
                    f.write(f"第 {page.page_number} 页\n")
                    f.write("-" * 20 + "\n\n")
                    
                    for element in page.elements:
                        if element.element_type == "text":
                            f.write(element.content + "\n\n")
                        elif element.element_type == "table":
                            f.write("表格内容:\n")
                            f.write(element.content + "\n\n")
                    
                    f.write("\n")
            
            return True
            
        except Exception as e:
            self.logger.error(f"文本导出失败: {e}")
            return False
    
    def get_supported_formats(self) -> List[str]:
        return [".txt"]

class MarkdownExporter(BaseExporter):
    """Markdown导出器"""
    
    def export(self, pages: List[DocumentPage], output_path: str, **kwargs) -> bool:
        """导出为Markdown"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                # 写入标题
                title = kwargs.get('title', '翻译文档')
                f.write(f"# {title}\n\n")
                
                # 写入页面内容
                for page in pages:
                    f.write(f"## 第 {page.page_number} 页\n\n")
                    
                    for element in page.elements:
                        if element.element_type == "text":
                            f.write(f"{element.content}\n\n")
                        elif element.element_type == "table":
                            f.write("### 表格\n\n")
                            # 转换为Markdown表格格式
                            table_md = self._convert_to_markdown_table(element.content)
                            f.write(table_md + "\n\n")
                    
                    f.write("\n")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Markdown导出失败: {e}")
            return False
    
    def _convert_to_markdown_table(self, content: str) -> str:
        """转换为Markdown表格格式"""
        try:
            lines = content.strip().split('\n')
            if not lines:
                return ""
            
            # 构建Markdown表格
            md_table = []
            for i, line in enumerate(lines):
                cells = line.split('\t')
                md_row = "| " + " | ".join(cells) + " |"
                md_table.append(md_row)
                
                # 添加表头分隔符
                if i == 0:
                    separator = "| " + " | ".join(["---"] * len(cells)) + " |"
                    md_table.append(separator)
            
            return "\n".join(md_table)
        except:
            return content
    
    def get_supported_formats(self) -> List[str]:
        return [".md"]

class ExporterFactory:
    """导出器工厂"""
    
    def __init__(self):
        self.exporters = {
            '.pdf': PDFExporter(),
            '.docx': DocxExporter(),
            '.txt': TxtExporter(),
            '.md': MarkdownExporter(),
        }
    
    def get_exporter(self, format_ext: str) -> Optional[BaseExporter]:
        """根据格式获取导出器"""
        return self.exporters.get(format_ext.lower())
    
    def get_supported_formats(self) -> List[str]:
        """获取所有支持的导出格式"""
        formats = []
        for exporter in self.exporters.values():
            formats.extend(exporter.get_supported_formats())
        return list(set(formats))
    
    def export_document(self, pages: List[DocumentPage], output_path: str, **kwargs) -> bool:
        """导出文档"""
        file_ext = Path(output_path).suffix.lower()
        exporter = self.get_exporter(file_ext)
        
        if exporter:
            return exporter.export(pages, output_path, **kwargs)
        else:
            raise ValueError(f"不支持的导出格式: {file_ext}")

class DocumentExporter:
    """文档导出管理器"""
    
    def __init__(self, config):
        self.config = config
        self.exporter_factory = ExporterFactory()
        self.logger = logging.getLogger(__name__)
    
    def export_translated_document(self, original_pages: List[DocumentPage], 
                                 translated_pages: List[DocumentPage],
                                 output_path: str, export_mode: str = "translated_only",
                                 **kwargs) -> bool:
        """导出翻译后的文档"""
        try:
            if export_mode == "translated_only":
                # 只导出翻译结果
                pages_to_export = translated_pages
            elif export_mode == "bilingual":
                # 导出双语对照
                pages_to_export = self._create_bilingual_pages(original_pages, translated_pages)
            elif export_mode == "original_only":
                # 只导出原文
                pages_to_export = original_pages
            else:
                raise ValueError(f"不支持的导出模式: {export_mode}")
            
            # 添加元数据
            kwargs.update({
                'title': kwargs.get('title', '翻译文档'),
                'export_mode': export_mode,
                'export_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            
            return self.exporter_factory.export_document(pages_to_export, output_path, **kwargs)
            
        except Exception as e:
            self.logger.error(f"文档导出失败: {e}")
            return False
    
    def _create_bilingual_pages(self, original_pages: List[DocumentPage], 
                              translated_pages: List[DocumentPage]) -> List[DocumentPage]:
        """创建双语对照页面"""
        bilingual_pages = []
        
        for i, (orig_page, trans_page) in enumerate(zip(original_pages, translated_pages)):
            elements = []
            
            # 交替添加原文和译文
            orig_elements = [e for e in orig_page.elements if e.element_type == "text"]
            trans_elements = [e for e in trans_page.elements if e.element_type == "text"]
            
            for j, (orig_elem, trans_elem) in enumerate(zip(orig_elements, trans_elements)):
                # 原文
                orig_element = DocumentElement(
                    element_type="text",
                    content=f"【原文】{orig_elem.content}",
                    position=(0, j * 40, 500, j * 40 + 15),
                    page_number=i + 1
                )
                elements.append(orig_element)
                
                # 译文
                trans_element = DocumentElement(
                    element_type="text",
                    content=f"【译文】{trans_elem.content}",
                    position=(0, j * 40 + 20, 500, j * 40 + 35),
                    page_number=i + 1
                )
                elements.append(trans_element)
            
            bilingual_page = DocumentPage(
                page_number=i + 1,
                elements=elements,
                width=orig_page.width,
                height=orig_page.height
            )
            bilingual_pages.append(bilingual_page)
        
        return bilingual_pages
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的导出格式"""
        return self.exporter_factory.get_supported_formats()